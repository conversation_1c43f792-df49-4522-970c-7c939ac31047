---
// Importing necessary components
import Meta from "@components/Meta.astro";
import Navbar from "@components/sections/Navbar.astro";
import FooterSection from "@components/sections/FooterSection.astro";
import { SITE } from "@data/constants";
import "@styles/global.css";
// Setting expected props
const { title = SITE.title, meta, structuredData, lang = "en" } = Astro.props;

// Interface to type-check the properties
interface Props {
  title?: string;
  meta?: string;
  structuredData?: object;
  lang?: string;
}
---

<!--
This is the main structure for the page.
We set the language of the page to English and add classes for scrollbar and scroll behavior.
-->
<html lang={lang} class="scrollbar-hide lenis lenis-smooth scroll-pt-16">
  <head>
    <!-- Adding metadata to the HTML document -->
    <Meta meta={meta} structuredData={structuredData} />
    <!-- Define the title of the page -->
    <title>{title}</title>
    <script is:inline>
      // Script to handle dark mode. It will check if the theme is stored in localStorage or if dark theme is preferred by system settings
      if (
        localStorage.getItem("hs_theme") === "dark" ||
        (!("hs_theme" in localStorage) &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        document.documentElement.classList.add("dark");
      } else {
        document.documentElement.classList.remove("dark");
      }
    </script>
    <script>
      import "@scripts/lenisSmoothScroll.js";
    </script>
  </head>
  <body
    class="bg-neutral-200 selection:bg-yellow-400 selection:text-neutral-700 dark:bg-neutral-800"
  >
    <!--
    Setting up the main structure of the page.
    The Navbar is placed at the top, with a slot for the main content and FooterSection at the bottom.
    -->
    <div class="mx-auto max-w-(--breakpoint-2xl) px-4 sm:px-6 lg:px-8">
      <Navbar />
      <main>
        <slot />
      </main>
    </div>
    <FooterSection />
    <script>
      // https://preline.co
      import "preline/preline.js";
    </script>
    <style>
      /* CSS rules for the page scrollbar */
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }

      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    </style>
  </body>
</html>
