---
// Import section components
import MainLayout from "@/layouts/MainLayout.astro";
import Btn404 from "@components/ui/buttons/Btn404.astro";
import { SITE } from "@data/constants";

const pageTitle: string = `Page Not Found | ${SITE.title}`;

// Define variables for page content
const title: string = "404";
const subTitle: string = "Oops, this isn't the tool you were looking for!";
const content: string =
  "Don't let this hiccup slow you down. Let's get you back to building your masterpiece.";
const btnTitle: string = "Go Back";
---

<MainLayout title={pageTitle}>
  <section class="grid h-svh place-content-center">
    <div class="mx-auto max-w-(--breakpoint-xl) px-4 py-8 lg:px-6 lg:py-16">
      <div class="mx-auto max-w-(--breakpoint-sm) text-center">
        <h1
          class="text-dark mb-4 text-7xl font-extrabold text-yellow-500 dark:text-yellow-400 lg:text-9xl"
        >
          {title}
        </h1>
        <p
          class="mb-4 text-balance text-3xl font-bold tracking-tight text-neutral-700 dark:text-neutral-300 md:text-4xl"
        >
          {subTitle}
        </p>

        <p
          class="mb-4 text-pretty text-lg text-neutral-600 dark:text-neutral-400"
        >
          {content}
        </p>
        <!--Display a button that navigates user back to the previous page-->
        <Btn404 title={btnTitle} id="go-back" />
      </div>
    </div>
  </section>
</MainLayout>
<!--JavaScript code that adds click event to the Button, resulting in going back to the previous page in history-->
<script>
  const goBackButton = document.getElementById("go-back") as HTMLButtonElement;

  goBackButton?.addEventListener("click", () => {
    history.back();
  });
</script>
