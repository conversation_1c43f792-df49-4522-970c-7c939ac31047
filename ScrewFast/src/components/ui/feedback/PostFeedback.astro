---
// Define props from Astro
const { title, firstChoice, secondChoice } = Astro.props;

// Define TypeScript interface for props
interface Props {
  title: string;
  firstChoice: string;
  secondChoice: string;
}
---

<div class="mt-12 flex items-center justify-center gap-x-2">
  <h3 class="text-neutral-700 dark:text-neutral-300">{title}</h3>
  <button
    type="button"
    class="group inline-flex items-center gap-x-2 rounded-lg border border-neutral-400 px-3 py-2 text-sm font-medium text-neutral-700 hover:border-yellow-500 hover:bg-yellow-500 hover:shadow-2xl hover:shadow-yellow-500 dark:border-neutral-500 dark:text-neutral-300 dark:hover:bg-yellow-500 dark:hover:text-neutral-700 dark:hover:border-yellow-500 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
  >
    <svg
      class="size-4 shrink-0 transition duration-300 group-hover:-translate-y-1 group-focus-visible:-translate-y-1"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      ><path d="M7 10v12"></path><path
        d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"
      ></path></svg
    >
    {firstChoice}
  </button>
  <button
    type="button"
    class="group inline-flex items-center gap-x-2 rounded-lg border border-neutral-400 px-3 py-2 text-sm font-medium text-neutral-700 hover:bg-neutral-400/30 dark:border-neutral-500 dark:text-neutral-300 dark:hover:bg-neutral-700/60 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600"
  >
    <svg
      class="size-4 shrink-0 transition duration-300 group-hover:translate-y-1 group-focus-visible:translate-y-1"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      ><path d="M17 14V2"></path><path
        d="M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z"
      ></path></svg
    >
    {secondChoice}
  </button>
</div>
