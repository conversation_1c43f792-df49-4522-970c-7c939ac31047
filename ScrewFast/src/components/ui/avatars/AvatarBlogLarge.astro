---
// Import necessary components
import { Image } from "astro:assets";

import type { CollectionEntry } from "astro:content";

const { blogEntry } = Astro.props;

interface Props {
  blogEntry: CollectionEntry<"blog">;
}
---

<div class="shrink-0">
  <Image
    class="size-10 rounded-full sm:h-14 sm:w-14"
    src={blogEntry.data.authorImage}
    alt={blogEntry.data.authorImageAlt}
    draggable={"false"}
    format={"avif"}
  />
</div>
