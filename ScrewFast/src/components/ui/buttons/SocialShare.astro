---
import Icon from "@components/ui/icons/Icon.astro";
// Destructure the properties from Astro.props
const { pageTitle } = Astro.props;

// Define TypeScript interface for the properties
interface Props {
  pageTitle: string;
}

type SocialPlatform = {
  name: string;
  url: string;
  svg: string;
};

const encoded_url = encodeURIComponent(Astro.url.href);

const socialPlatforms: SocialPlatform[] = [
  {
    name: "Facebook",
    url: `https://www.facebook.com/sharer/sharer.php?u=${encoded_url}`,
    svg: "facebook",
  },
  {
    name: "X",
    url: `https://twitter.com/intent/tweet?url=${encoded_url}&text=${pageTitle}`,
    svg: "x",
  },
  {
    name: "LinkedIn",
    url: `https://www.linkedin.com/sharing/share-offsite/?url=${encoded_url}&title=${pageTitle}`,
    svg: "linkedIn",
  },
];
---

<div
  class="hs-dropdown relative inline-flex [--auto-close:inside] [--placement:top-left]"
>
  <button
    id="hs-dropup"
    type="button"
    class="hs-dropdown-toggle inline-flex items-center gap-x-2 rounded-lg px-4 py-3 text-sm font-medium text-neutral-600 outline-hidden ring-zinc-500 transition duration-300 hover:bg-neutral-100 hover:text-neutral-700 focus-visible:ring-3 dark:text-neutral-400 dark:ring-zinc-200 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:outline-hidden"
  >
    <Icon name="share" />

    Share
  </button>

  <div
    class="hs-dropdown-menu duration z-10 hidden w-72 divide-y divide-neutral-200 rounded-lg bg-neutral-50 p-2 opacity-0 shadow-md transition-[opacity,margin] hs-dropdown-open:opacity-100 dark:divide-neutral-700 dark:border dark:border-neutral-700 dark:bg-neutral-800"
    aria-labelledby="hs-dropup"
  >
    <div class="py-2 first:pt-0 last:pb-0">
      {
        socialPlatforms.map((platform) => (
          <a
            class="flex items-center gap-x-3.5 rounded-lg px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-200 focus:bg-neutral-100  focus:outline-hidden dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 "
            href={platform.url}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={`Share on ${platform.name}`}
          >
            <Icon name={platform.svg} />
            Share on {platform.name}
          </a>
        ))
      }
    </div>
    <div class="py-2 first:pt-0 last:pb-0">
      <button
        type="button"
        class="js-clipboard hover:text-dark focus-visible:ring-secondary group inline-flex w-full items-center gap-x-3.5 rounded-lg px-3 py-2 text-sm text-neutral-700 hover:bg-neutral-200 focus:bg-neutral-100 focus:outline-hidden focus-visible:outline-hidden focus-visible:ring-1 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700"
        data-clipboard-success-text="Copied"
      >
        <svg
          class="js-clipboard-default h-4 w-4 transition group-hover:rotate-6"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <rect width="8" height="4" x="8" y="2" rx="1" ry="1"></rect>
          <path
            d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"
          ></path>
        </svg>

        <svg
          class="js-clipboard-success text-primary-accent-light dark:text-primary-accent-dark hidden h-4 w-4"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
        <span class="js-clipboard-success-text">Copy link</span>
      </button>
    </div>
  </div>
</div>

<script>
  import ClipboardJS from "clipboard";

  // Initialization of Clipboard
  (function () {
    window.addEventListener("load", () => {
      const clipboards =
        document.querySelectorAll<HTMLElement>(".js-clipboard");

      clipboards.forEach((el) => {
        const clipboard = new ClipboardJS(el, {
          text: () => {
            return window.location.href;
          },
        });

        clipboard.on("success", () => {
          const defaultElement = el.querySelector<HTMLElement>(
            ".js-clipboard-default"
          );
          const successElement = el.querySelector<HTMLElement>(
            ".js-clipboard-success"
          );
          const successTextElement = el.querySelector<HTMLElement>(
            ".js-clipboard-success-text"
          );
          const successText = el.dataset.clipboardSuccessText || "";
          let oldSuccessText: string | undefined;

          if (successTextElement) {
            oldSuccessText = successTextElement.textContent || "";
            successTextElement.textContent = successText;
          }

          if (defaultElement && successElement) {
            defaultElement.style.display = "none";
            successElement.style.display = "block";
          }

          setTimeout(() => {
            if (successTextElement && oldSuccessText !== undefined) {
              successTextElement.textContent = oldSuccessText;
            }

            if (defaultElement && successElement) {
              successElement.style.display = "";
              defaultElement.style.display = "";
            }
          }, 800);
        });
      });
    });
  })();
</script>
