---
// Destructure the properties from Astro.props
const { id, dataTab, title, first } = Astro.props;

// Define TypeScript interface for the properties
interface Props {
  id: string;
  dataTab: string;
  title: string;
  first?: boolean;
}
// Define constants for styling classes
const BUTTON_CLASS =
  "flex w-full justify-center rounded-xl border border-transparent p-3 outline-hidden ring-zinc-500 transition duration-300 hover:bg-neutral-100 focus-visible:ring-3 dark:ring-zinc-200 dark:hover:bg-neutral-700 dark:focus:outline-hidden md:p-5";

const HEADING_CLASS = "block text-center font-bold";
const INACTIVE_HEADING_CLASS = "text-neutral-800 dark:text-neutral-200";
---

<!-- Tab button element -->
<button
  type="button"
  class={`${BUTTON_CLASS} ${first ? "active bg-neutral-100 hover:border-transparent dark:bg-white/[.05]" : ""}`}
  id={id}
  data-target={dataTab}
  role="tab"
>
  <!-- Tab text -->
  <span
    class={`${HEADING_CLASS} ${first ? "text-orange-400 dark:text-orange-300" : INACTIVE_HEADING_CLASS}`}
  >
    {title}
  </span>
</button>
