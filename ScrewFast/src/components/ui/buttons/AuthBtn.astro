---
// Destructure the properties from Astro.props
const { title } = Astro.props;

// Define TypeScript interface for the properties
interface Props {
  title: string;
}
// Define CSS classes for styling the button
const baseClasses =
  "inline-flex w-full items-center justify-center gap-x-2 rounded-lg px-4 py-3 text-sm font-bold text-neutral-700 focus-visible:ring-3 outline-hidden transition duration-300";
const borderClasses = "border border-transparent";
const bgColorClasses = "bg-yellow-400 dark:focus:outline-hidden";
const hoverClasses = "hover:bg-yellow-500";
const fontSizeClasses = "2xl:text-base";
const disabledClasses = "disabled:pointer-events-none disabled:opacity-50";
const ringClasses = "ring-zinc-500 dark:ring-zinc-200";
---

<!-- Styled submit button with dynamic title -->
<button
  type="submit"
  class={`${baseClasses} ${borderClasses} ${bgColorClasses} ${hoverClasses} ${fontSizeClasses} ${disabledClasses} ${ringClasses}`}
  >{title}</button
>
