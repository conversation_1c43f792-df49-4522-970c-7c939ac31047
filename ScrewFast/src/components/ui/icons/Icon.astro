---
import { Icons } from "./icons.ts";

interface Path {
  d: string;
  class?: string;
}

const { name } = Astro.props;

let icon = (Icons as any)[name] || {};

let paths: Path[] = icon.paths || [];
---

{
  icon ? (
    <svg
      class={icon.class}
      height={icon.height}
      viewBox={icon.viewBox}
      width={icon.width}
      fill={icon.fill}
      clip-rule={icon.clipRule}
      fill-rule={icon.fillRule}
      stroke={icon.stroke}
      stroke-width={icon.strokeWidth}
      stroke-linecap={icon.strokeLinecap}
      stroke-linejoin={icon.strokeLinejoin}
    ><title>{icon.title}</title>
      {paths.map((path: Path) => (
        <path d={path.d} class={path.class || ""} />
      ))}
    </svg>
  ) : (
    "Icon not found"
  )
}
