---
const { label = "Email", id } = Astro.props;

interface Props {
  label?: string;
  id: string;
}
---

<!-- Container for the label and email input field -->
<div>
  <!-- Label for the email input field, visually hidden but accessible to screen readers -->
  <label for={id} class="sr-only">{label}</label>
  <!-- Email input field -->
  <input
    type="email"
    name="hs-email-contacts"
    id={id}
    autocomplete="email"
    class="block w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-3 text-sm text-neutral-700 placeholder:text-neutral-500 focus:border-neutral-200 focus:outline-hidden focus:ring-3 focus:ring-neutral-400 disabled:pointer-events-none disabled:opacity-50 dark:border-neutral-600 dark:bg-neutral-700/30 dark:text-neutral-300 dark:placeholder:text-neutral-400 dark:focus:ring-1"
    placeholder="Email"
  />
</div>
