---
const {
  label = "Search",
  title = "Subscribe",
  id = "footer-input",
} = Astro.props;

interface Props {
  label?: string;
  title?: string;
  id?: string;
}

const placeholder = "Enter your email";
---

<div
  class="mt-4 flex flex-col items-center gap-2 rounded-lg bg-neutral-200 p-2 dark:bg-neutral-800 sm:flex-row sm:gap-3"
>
  <div class="w-full">
    <label for={id} class="sr-only">{label}</label>
    <input
      type="text"
      id={id}
      name="footer-input"
      class="block w-full rounded-lg border-transparent bg-neutral-100 px-4 py-3 text-sm text-neutral-600 focus:border-orange-400 focus:ring-orange-400 disabled:pointer-events-none disabled:opacity-50 dark:border-transparent dark:bg-neutral-700 dark:text-gray-300 dark:placeholder:text-neutral-300"
      placeholder={placeholder}
    />
  </div>
  <a
    class="inline-flex w-full items-center justify-center gap-x-2 whitespace-nowrap rounded-lg border border-transparent bg-orange-400 p-3 text-sm font-bold text-neutral-50 outline-hidden ring-zinc-500 transition duration-300 hover:bg-orange-500 focus-visible:ring-3 disabled:pointer-events-none disabled:opacity-50 dark:ring-zinc-200 dark:focus:outline-hidden dark:focus:ring-1 sm:w-auto"
    href="#"
  >
    {title}
  </a>
</div>
