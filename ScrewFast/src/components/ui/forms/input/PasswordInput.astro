---
const {
  label = "Password",
  forgot,
  id,
  errorId,
  content
} = Astro.props;

interface Props {
  label?: string;
  forgot?: boolean;
  id?: string;
  errorId?: string;
  content?: string;
}
---

<div>
  <div class="flex items-center justify-between">
    <label
      for={id}
      class="mb-2 block text-sm text-neutral-800 dark:text-neutral-200"
      >{label}</label
    >
    {
      forgot ? (
        <button
          class="rounded-lg text-sm font-medium text-orange-400 decoration-2 outline-hidden ring-zinc-500 hover:underline focus-visible:ring-3 dark:text-orange-400 dark:ring-zinc-200 dark:focus:outline-hidden dark:focus:ring-1"
          data-hs-overlay="#hs-toggle-between-modals-recover-modal"
        >
          Forgot password?
        </button>
      ) : (
        ""
      )
    }
  </div>
  <div class="relative">
    <input
      type="password"
      id={id}
      name="password"
      class="block w-full rounded-lg border border-neutral-200 bg-neutral-50 px-4 py-3 text-sm text-neutral-700 focus:border-neutral-200 focus:outline-hidden focus:ring-3 focus:ring-neutral-400 disabled:pointer-events-none disabled:opacity-50 dark:border-neutral-600 dark:bg-neutral-700/30 dark:text-neutral-300 dark:focus:ring-1"
      required
      aria-describedby={errorId}
    />
    <div class="pointer-events-none absolute inset-y-0 end-0 hidden pe-3">
      <svg
        class="h-5 w-5 text-red-500"
        width="16"
        height="16"
        fill="currentColor"
        viewBox="0 0 16 16"
        aria-hidden="true"
      >
        <path
          d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"
        ></path>
      </svg>
    </div>
  </div>
  <p class="mt-2 hidden text-xs text-red-600" id={errorId}>
    {content}
  </p>
</div>
