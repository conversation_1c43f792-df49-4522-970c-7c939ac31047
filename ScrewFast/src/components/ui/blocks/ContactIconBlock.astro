---
// Define props from Astro
const {
  heading,
  content,
  isAddressVisible,
  addressContent,
  isLinkVisible,
  linkTitle,
  linkURL,
  isArrowVisible,
} = Astro.props;

// Define TypeScript interface for props
interface Props {
  heading?: string;
  content?: string;
  isAddressVisible?: boolean;
  addressContent?: string;
  isLinkVisible?: boolean;
  linkTitle?: string;
  linkURL?: string;
  isArrowVisible?: boolean;
}

// Define SVG arrow to be used in the component
const arrowSVG: string = `<svg
class="h-4 w-4 shrink-0 transition ease-in-out group-hover:translate-x-1"
fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" >
<path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" /> </svg>`;
---

<!-- Root container, which arranges the heading and content -->
<div class="flex gap-x-7 py-6">
  <!-- Slot to allow for extensibility of the component -->
  <slot />
  <div class="grow">
    <!-- Heading of the section -->
    <h3 class="font-bold text-neutral-700 dark:text-neutral-300">
      {heading}
    </h3>
    <!-- Content of the section -->
    <p class="mt-1 text-sm text-neutral-600 dark:text-neutral-400">{content}</p>
    <!-- Conditional rendering of address content if isAddressVisible is true -->
    {
      isAddressVisible ? (
        <p class="mt-1 text-sm italic text-neutral-500">{addressContent}</p>
      ) : null
    }
    <!-- Conditional rendering of a link if isLinkVisible is true.
         The link also conditionally includes an arrow SVG if isArrowVisible is true -->
    {
      isLinkVisible ? (
        <a
          class="group mt-2 inline-flex items-center gap-x-2 rounded-lg text-sm font-medium text-zinc-600 outline-hidden ring-zinc-500 transition duration-300 hover:text-zinc-800 focus-visible:ring-3 dark:text-zinc-400 dark:ring-zinc-200 dark:hover:text-zinc-200 dark:focus:outline-hidden dark:focus:ring-1 "
          href={linkURL}
        >
          {linkTitle}
          {isArrowVisible ? <Fragment set:html={arrowSVG} /> : null}
        </a>
      ) : null
    }
  </div>
</div>
