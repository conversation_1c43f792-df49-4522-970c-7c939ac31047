---
import Avatar from "@components/ui/avatars/Avatar.astro";
import FullStar from "@components/ui/stars/FullStar.astro";
import HalfStar from "@components/ui/stars/HalfStar.astro";

const { avatars, starCount = 0, rating, reviews } = Astro.props;

interface Props {
  avatars?: Array<string>;
  starCount?: number;
  rating?: string;
  reviews?: string;
}
---

<div class="mt-6 lg:mt-10">
  <div class="py-5">
    <div class="text-center sm:flex sm:items-center sm:text-start">
      <div class="shrink-0 pb-5 sm:flex sm:pb-0 sm:pe-5">
        <!-- Avatar Group -->
        <div class="flex justify-center -space-x-3">
          {avatars?.map((src) => <Avatar src={src} alt="Avatar Description" />)}
          <span
            class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-zinc-800 ring-2 ring-white dark:bg-zinc-900 dark:ring-zinc-800"
          >
            <span class="text-xs font-medium uppercase leading-none text-white"
              >7k+</span
            >
          </span>
        </div>
      </div>
      <div
        class="mx-auto h-px w-32 border-t border-neutral-400 dark:border-neutral-500 sm:mx-0 sm:h-8 sm:w-auto sm:border-s sm:border-t-0"
      >
      </div>
      <!-- Review Ratings -->
      <div class="flex flex-col items-center sm:items-start">
        <div class="flex items-baseline space-x-1 pt-5 sm:ps-5 sm:pt-0">
          <div class="flex space-x-1">
            <!-- Your star ratings -->
            {
              Array(starCount)
                .fill(0)
                .map((_, i) => <FullStar key={i} />)
            }
            <!-- Adding additional half-star -->
            <HalfStar />
          </div>
          <p class="text-neutral-800 dark:text-neutral-200">
            <Fragment set:html={rating} />
          </p>
        </div>
        <div class="text-sm text-neutral-800 dark:text-neutral-200 sm:ps-5">
          <p>
            <Fragment set:html={reviews} />
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
