---
// Import the necessary dependencies
import FooterSocialLink from "@components/ui/links/FooterSocialLink.astro";
import EmailFooterInput from "@components/ui/forms/input/EmailFooterInput.astro";
import strings from "@utils/navigation.ts";
import Icon from "@components/ui/icons/Icon.astro";
import Brand<PERSON><PERSON> from "@components/BrandLogo.astro";
import { SITE } from "@data/constants";

// Define the variables that will be used in this component
const sectionThreeTitle: string = "Stay up to date";
const sectionThreeContent: string = "Stay updated with the latest tools and exclusive deals.";
const crafted: string = "Crafted by";
---

<footer class="w-full bg-neutral-300 dark:bg-neutral-900">
  <div
    class="mx-auto w-full max-w-[85rem] px-4 py-10 sm:px-6 lg:px-16 lg:pt-20 2xl:max-w-(--breakpoint-2xl)"
  >
    <div class="grid grid-cols-2 gap-6 md:grid-cols-4 lg:grid-cols-5">
      <div class="col-span-full lg:col-span-1">
        <!-- Brand Logo -->
        <BrandLogo class="h-auto w-32" />
      </div>
      <!-- An array of links for Product and Company sections -->
      {
        strings.footerLinks.map((section) => (
          <div class="col-span-1">
            <h3 class="font-bold text-neutral-800 dark:text-neutral-200">
              {section.section}
            </h3>
            <ul class="mt-3 grid space-y-3">
              {section.links.map((link, index) => (
                <li>
                  <a
                    href={link.url}
                    class="inline-flex gap-x-2 rounded-lg text-neutral-600 outline-hidden ring-zinc-500 transition duration-300 hover:text-neutral-500 focus-visible:ring-3 dark:text-neutral-400 dark:ring-zinc-200 dark:hover:text-neutral-300 dark:focus:outline-hidden"
                  >
                    {link.name}
                  </a>
                  {section.section === "Company" && index === 2 ? (
                    <span class="ms-1 inline rounded-lg bg-orange-500 px-2 py-1 text-xs font-bold text-neutral-50">
                      We're hiring!
                    </span>
                  ) : null}
                </li>
              ))}
            </ul>
          </div>
        ))
      }

      <div class="col-span-2">
        <h3 class="font-bold text-neutral-800 dark:text-neutral-200">
          {sectionThreeTitle}
        </h3>

        <form>
          <EmailFooterInput />
          <p class="mt-3 text-sm text-neutral-600 dark:text-neutral-400">
            {sectionThreeContent}
          </p>
        </form>
      </div>
    </div>

    <div
      class="mt-9 grid gap-y-2 sm:mt-12 sm:flex sm:items-center sm:justify-between sm:gap-y-0"
    >
      <div class="flex items-center justify-between">
        <p class="text-sm text-neutral-600 dark:text-neutral-400">
          © <span id="current-year"></span> {SITE.title}. {crafted}
          <a
            class="rounded-lg font-medium underline underline-offset-2 outline-hidden ring-zinc-500 transition duration-300 hover:text-neutral-700 hover:decoration-dashed focus:outline-hidden focus-visible:ring-3 dark:ring-zinc-200 dark:hover:text-neutral-300"
            href="https://sobstvennoai.dev"
            target="_blank"
            rel="noopener noreferrer">sobstvennoAI</a
          >.
        </p>
      </div>

      <!-- Social Brands -->
      <div>
        <FooterSocialLink url={strings.socialLinks.facebook}
          ><Icon name="facebookFooter" />
        </FooterSocialLink>

        <FooterSocialLink url={strings.socialLinks.x}
          ><Icon name="xFooter" /></FooterSocialLink
        >

        <FooterSocialLink url={strings.socialLinks.github}
          ><Icon name="githubFooter" />
        </FooterSocialLink>

        <FooterSocialLink url={strings.socialLinks.google}
          ><Icon name="googleFooter" />
        </FooterSocialLink>

        <FooterSocialLink url={strings.socialLinks.slack}
          ><Icon name="slackFooter" />
        </FooterSocialLink>
      </div>
    </div>

    <script>
      const year = new Date().getFullYear();
      const element = document.getElementById("current-year");
      element!.innerText = year.toString();
    </script>
  </div>
</footer>
