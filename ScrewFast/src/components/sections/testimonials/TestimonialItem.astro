---
import { Image } from "astro:assets";
import Icon from "@components/ui/icons/Icon.astro";

const { content, author, role, avatarSrc } = Astro.props;

interface Props {
  content: string;
  author: string;
  role: string;
  avatarSrc: string;
}
---

<blockquote class="relative">
  <Icon name="quotation" />

  <div class="relative z-10">
    <p class="text-xl italic text-neutral-800 dark:text-neutral-200">
      {content}
    </p>
  </div>

  <div class="mt-6">
    <div class="flex items-center">
      <div class="shrink-0">
        <Image
          class="h-8 w-8 rounded-full"
          src={avatarSrc}
          alt="Avatar Description"
          loading={"eager"}
          inferSize
        />
      </div>
      <div class="ms-4 grow">
        <div class="font-bold text-neutral-800 dark:text-neutral-200">
          {author}
        </div>
        <div class="text-xs text-neutral-500">{role}</div>
      </div>
    </div>
  </div>
</blockquote>
