---
// Import AvatarTestimonialSection component for use in this module
import AvatarTestimonialSection from "@components/ui/avatars/AvatarTestimonialSection.astro";

// Define props from Astro
const { title, testimonials } = Astro.props;

// Define TypeScript interface for Testimonial
interface Testimonial {
  content: string;
  author: string;
  role: string;
  avatarSrc: string;
  avatarAlt: string;
}

// Define TypeScript interface for props
interface Props {
  title: string;
  testimonials: Testimonial[];
}
---
<!-- Main div that wraps the testimonials section -->
<section
  class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
  id="testimonials"
>
  <!-- Title of the testimonials section -->
  <div class="mb-6 w-3/4 max-w-2xl sm:mb-10 md:mb-16 lg:w-1/2">
    <h2
      class="text-balance text-2xl font-bold text-neutral-800 dark:text-neutral-200 sm:text-3xl lg:text-4xl"
    >
      {title}
    </h2>
  </div>

  <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
    <!-- Looping through each testimonial data and rendering it -->
    {
      testimonials.map((testimonial) => (
        <div class="flex h-auto">
          <div class="flex flex-col rounded-xl bg-neutral-50 dark:bg-neutral-700">
            <div class="flex-auto p-4 md:p-6">
              <!-- Testimonial content -->
              <p class="text-pretty text-base italic text-neutral-600 dark:text-neutral-300 md:text-lg">
                {testimonial.content}
              </p>
            </div>

            <div class="rounded-b-xl bg-neutral-300/30 p-4 dark:bg-neutral-900/30 md:px-7">
              <div class="flex items-center">
                <AvatarTestimonialSection
                  src={testimonial.avatarSrc}
                  alt={testimonial.avatarAlt}
                />

                <div class="ms-3 grow">
                  <p class="text-sm font-bold text-neutral-800 dark:text-neutral-200 sm:text-base">
                    {testimonial.author}
                  </p>
                  <p class="text-xs text-neutral-600 dark:text-neutral-400">
                    {testimonial.role}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))
    }
  </div>
</section>
