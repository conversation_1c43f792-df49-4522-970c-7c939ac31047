---
// Import SecondaryCTA component for use in this module
import SecondaryCTA from "@components/ui/buttons/SecondaryCTA.astro";
import Icon from "@components/ui/icons/Icon.astro";

// Define props from Astro
const { pricing } = Astro.props;

// Define TypeScript type for products.
type Product = {
  name: string;
  description: string;
  price: string;
  cents: string;
  billingFrequency: string;
  features: Array<string>;
  purchaseBtnTitle: string;
  purchaseLink: string;
};

interface PricingSectionProps {
  title: string;
  subTitle: string;
  badge: string;
  thirdOption: string;
  btnText: string;
  pricing: {
    title: string;
    subTitle: string;
    starterKit: Product;
    professionalToolbox: Product;
  };
}
---

<section
  class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
>
  <!-- Section heading and sub-heading -->
  <div class="mx-auto mb-10 max-w-2xl text-center lg:mb-14">
    <h2
      class="text-balance text-2xl font-bold tracking-tight text-neutral-800 dark:text-neutral-200 md:text-4xl md:leading-tight"
    >
      {pricing.title}
    </h2>
    <p class="mt-1 text-pretty text-neutral-600 dark:text-neutral-400">
      {pricing.subTitle}
    </p>
  </div>
  <!-- Contains two main product blocks -->
  <div class="flex flex-wrap items-center justify-center gap-4 sm:gap-0">
    <!-- Starter Kit product details -->
    <div
      class="w-full rounded-xl bg-gray-800 p-6 sm:w-1/2 sm:rounded-r-none sm:p-8 lg:w-1/3"
    >
      <div class="mb-4">
        <h3 class="text-2xl font-bold text-neutral-100 sm:text-3xl">
          {pricing.starterKit.name}
        </h3>
        <p class="text-indigo-300">{pricing.starterKit.description}</p>
      </div>

      <div class="mb-4">
        <span class="text-4xl font-bold text-neutral-200"
          >{pricing.starterKit.price}</span
        >
        <span class="text-lg font-bold text-neutral-300"
          >{pricing.starterKit.cents}</span
        >
        <span class="ms-3 text-sm text-indigo-200"
          >{pricing.starterKit.billingFrequency}</span
        >
      </div>
      <!-- Features list - automatically created by mapping over `features` array -->
      <ul class="mb-6 space-y-2 text-neutral-300">
        {
          pricing.starterKit.features.map((feature: string) => (
            <li class="flex items-center gap-1.5">
              <Icon name="checkCircle" />

              <span>{feature}</span>
            </li>
          ))
        }
      </ul>
      <!-- CTA for purchasing the product -->
      <a
        href={pricing.starterKit.purchaseLink}
        class="block rounded-lg bg-gray-500 px-8 py-3 text-center text-sm font-bold text-gray-100 outline-hidden ring-indigo-300 transition duration-300 hover:bg-gray-600 focus-visible:ring-3 active:text-gray-300 md:text-base"
        >{pricing.starterKit.purchaseBtnTitle}</a
      >
    </div>
    <!-- Professional Toolbox product details -->
    <div
      class="w-full rounded-xl bg-linear-to-tr from-[#FF512F] to-[#F09819] p-6 shadow-xl sm:w-1/2 sm:p-8"
    >
      <div
        class="mb-4 flex flex-col items-start justify-between gap-4 lg:flex-row"
      >
        <div>
          <h3 class="text-2xl font-bold text-neutral-100 sm:text-3xl">
            {pricing.professionalToolbox.name}
          </h3>
          <p class="text-orange-200">{pricing.professionalToolbox.description}</p>
        </div>

        <span
          class="order-first inline-block rounded-full bg-orange-200/60 bg-opacity-50 px-3 py-1 text-xs font-bold uppercase tracking-wider text-center text-orange-600 lg:order-none"
          >{pricing.badge}</span
        >
      </div>

      <div class="mb-4">
        <span class="text-6xl font-bold text-neutral-200"
          >{pricing.professionalToolbox.price}</span
        >
        <span class="text-lg font-bold text-orange-100"
          >{pricing.professionalToolbox.cents}</span
        >
        <span class="ms-3 text-orange-200"
          >{pricing.professionalToolbox.billingFrequency}</span
        >
      </div>
      <!-- Features list - automatically created by mapping over `features` array -->
      <ul class="mb-6 space-y-2 text-orange-100">
        {
          pricing.professionalToolbox.features.map((feature: string) => (
            <li class="flex items-center gap-1.5">
              <Icon name="checkCircle" />

              <span>{feature}</span>
            </li>
          ))
        }
      </ul>
      <!-- CTA for purchasing the product -->
      <a
        href={pricing.professionalToolbox.purchaseLink}
        class="block rounded-lg bg-orange-200/40 bg-opacity-50 px-8 py-3 text-center text-sm font-bold text-neutral-100 outline-hidden ring-orange-300 transition duration-300 hover:bg-orange-300 focus-visible:ring-3 active:bg-orange-400 md:text-base"
        >{pricing.professionalToolbox.purchaseBtnTitle}</a
      >
    </div>
  </div>
  <!-- Call to action for Enterprise Solutions -->
  <div class="mt-8 flex items-center justify-center gap-x-3 md:mt-12">
    <p class="text-sm text-neutral-600 dark:text-neutral-400">
      {pricing.thirdOption}
    </p>

    <SecondaryCTA title={pricing.btnText} url="#" />
  </div>
</section>
