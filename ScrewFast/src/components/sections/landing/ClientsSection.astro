---
// Define props from Astro
const { title, subTitle, partners } = Astro.props;

interface Partner {
  icon: any;
  name?: string;
  href?: string;
}

// Define TypeScript interface for props
interface Props {
  title: string;
  subTitle?: string;
  partners: Partner[];
}
---

<section
  class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
>
  <!-- Title and description -->
  <div class="mx-auto mb-6 w-full space-y-1 text-center sm:w-1/2 lg:w-1/3">
    <h2
      class="text-balance text-2xl font-bold leading-tight text-neutral-800 dark:text-neutral-200 sm:text-3xl"
    >
      {title}
    </h2>
    {
      subTitle && (
        <p class="text-pretty leading-tight text-neutral-600 dark:text-neutral-400">
          {subTitle}
        </p>
      )
    }
  </div>
  <div
    class="flex flex-col items-center justify-center gap-y-2 sm:flex-row sm:gap-x-12 sm:gap-y-0 lg:gap-x-24"
  >
    <!-- Clients Group SVGs -->
    {
      partners.map((partner) => (
        <a href={partner.href} target="_blank" rel="noopener noreferrer">
              <div set:html={partner.icon} />
        </a>
      ))
    }
  </div>
</section>