---
// Import the necessary AccordionItem component and JSON data
import AccordionItem from "@components/ui/blocks/AccordionItem.astro";

// Define props from Astro
const { title, faqs } = Astro.props;

// Define TypeScript interface for props
interface Faq {
  question: string;
  answer: string;
}

interface FaqGroup {
  subTitle?: string;
  faqs: Faq[];
}

interface Props {
  title: string;
  faqs: FaqGroup;
}
// Define a helper function to generate ids dynamically.
const makeId = (base: any, index: any) => `${base}${index + 1}`;
---

<!-- Main container that holds all content. Customized for different viewport sizes. -->
<section
  class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
>
  <div class="grid gap-10 md:grid-cols-5">
    <div class="md:col-span-2">
      <div class="max-w-xs">
        <h2
          class="text-2xl font-bold text-neutral-800 dark:text-neutral-200 md:text-4xl md:leading-tight"
        >
          <Fragment set:html={title} />
        </h2>
        <p class="mt-1 hidden text-neutral-600 dark:text-neutral-400 md:block">
          {faqs.subTitle}
        </p>
      </div>
    </div>
    <!-- FAQ accordion items -->
    <div class="md:col-span-3">
      <div
        class="hs-accordion-group divide-y divide-neutral-200 dark:divide-neutral-700"
      >
        {
          faqs.faqs.map((question, i) => {
            // Generate ids dynamically for each FAQ accordion item.
            let id = makeId(
              "hs-basic-with-title-and-arrow-stretched-heading-",
              i
            );
            let collapseId = makeId(
              "hs-basic-with-title-and-arrow-stretched-collapse",
              i
            );

            return (
              <AccordionItem
                {...question}
                id={id}
                collapseId={collapseId}
                first={i === 0}
              />
            );
          })
        }
      </div>
    </div>
  </div>
</section>
