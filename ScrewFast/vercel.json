{"headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self'; base-uri 'self'; form-action 'self'; frame-src 'self'; frame-ancestors 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://images.unsplash.com; connect-src 'self'; object-src 'none'; upgrade-insecure-requests; block-all-mixed-content"}, {"key": "Permissions-Policy", "value": "interest-cohort=()"}, {"key": "Referrer-Policy", "value": "no-referrer-when-downgrade"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}]}]}