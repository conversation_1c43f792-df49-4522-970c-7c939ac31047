{"name": "", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build && node process-html.mjs", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/sitemap": "^3.2.1", "@astrojs/starlight": "^0.31.1", "@tailwindcss/vite": "^4.0.9", "astro": "^5.1.9", "astro-compressor": "^0.4.1", "clipboard": "^2.0.11", "globby": "^14.0.1", "gsap": "^3.12.5", "html-minifier-terser": "^7.2.0", "lenis": "^1.1.22", "preline": "^3.0.0", "sharp": "^0.33.3", "sharp-ico": "^0.1.5", "tailwindcss": "^4.0.9"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "astro-vtbot": "^1.7.2", "prettier": "^3.2.5", "prettier-plugin-astro": "^0.13.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "^5.4.3"}}