// @ts-check
import { defineConfig } from 'astro/config';
import mdx from '@astrojs/mdx';

// https://astro.build/config
export default defineConfig({
  output: 'static',
  // The site property should be your final deployed URL
  site: process.env.SITE || 'https://alieutica.com',
  // Only use base path for GitHub Pages deployments
  // For Netlify/Vercel, leave this undefined (no base path)
  base: process.env.BASE_PATH || undefined,
  integrations: [mdx()],
  vite: {
    define: {
      __CONTACT_API_URL__: JSON.stringify(process.env.CONTACT_API_URL || 'https://multi-site-contact-worker.emiola.workers.dev/api/contact')
    }
  },
  markdown: {
    shikiConfig: {
      theme: 'github-dark',
      wrap: true,
    },
  },
});
