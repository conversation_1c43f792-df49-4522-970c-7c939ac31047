{"name": "aliutica-com", "type": "module", "version": "1.1.2", "description": "Alieutica website", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "check": "astro check", "lint": "eslint . --ext .js,.ts,.astro", "format": "prettier --write ."}, "author": "@alieutica", "license": "none", "homepage": "https://alieutica.com/", "repository": {"type": "git", "url": "git+https://github.com/alieutica/alieutica.git"}, "bugs": {"url": "https://github.com/alieutica/alieutica/issues"}, "keywords": ["astro", "blog", "theme", "minimal", "typography"], "dependencies": {"@astrojs/mdx": "^4.3.3", "@fontsource/jetbrains-mono": "^5.2.6", "@fontsource/oswald": "^5.2.6", "@fontsource/roboto-condensed": "^5.2.6", "@fontsource/work-sans": "^5.2.6", "astro": "^5.12.8"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-plugin-astro": "^1.3.1", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "typescript": "^5.8.3", "wrangler": "^4.27.0"}}