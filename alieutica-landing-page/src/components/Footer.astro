---
import { config } from '../config';

const currentYear = new Date().getFullYear();
---

<footer class="site-footer">
  <div class="footer-content">
    <div class="footer-info">
      <p class="copyright">
        © {currentYear} {config.title} LLC. All rights reserved.
      </p>
    </div>

    <div class="social-links">

      <!-- {config.social.github && (
        <a href={config.social.github} target="_blank" rel="noopener" aria-label="GitHub" class="social-link">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
      )} -->

      {config.social.email && (
        <a href={`mailto:${config.social.email}`} aria-label="Email" class="social-link">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12.713l-11.985-9.713h23.97l-11.985 9.713zm0 2.574l-12-9.725v15.438h24v-15.438l-12 9.725z"/>
          </svg>
        </a>
      )}
    </div>
  </div>
</footer>

<style>
  .site-footer {
    background-color: var(--color-surface);
    border-top: 2px solid var(--color-border);
    margin-top: auto;
    padding: calc(var(--grid-unit) * 4) 0;
  }

  .footer-content {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) * 2);
  }

  .footer-info {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) / 2);
  }

  .copyright,
  .theme-credit {
    margin: 0;
    font-size: 0.9rem;
    color: var(--color-text-muted);
  }

  .theme-credit a {
    color: var(--color-accent);
    font-weight: 500;
  }

  .theme-credit a:hover,
  .theme-credit a:focus {
    color: var(--color-text-primary);
  }

  .social-links {
    display: flex;
    gap: calc(var(--grid-unit) * 1.5);
    align-items: center;
  }

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--color-text-primary);
    color: var(--color-background);
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .social-link:hover,
  .social-link:focus {
    background-color: var(--color-accent);
    transform: translateY(-2px);
    text-decoration: none;
  }

  .social-link svg {
    width: 20px;
    height: 20px;
  }

  @media (max-width: 768px) {
    .footer-content {
      flex-direction: column;
      text-align: center;
      gap: calc(var(--grid-unit) * 2);
    }

    .footer-info {
      order: 2;
    }

    .social-links {
      order: 1;
    }
  }
</style>