---
import { config } from '../config';

const base = import.meta.env.BASE_URL;

const mdPostsGlob = import.meta.glob('../content/blog/*.md', { eager: true });
const mdxPostsGlob = import.meta.glob('../content/blog/*.mdx', { eager: true });
const allPosts = [...Object.values(mdPostsGlob), ...Object.values(mdxPostsGlob)] as any[];
const recentPosts = allPosts
  .sort((a: any, b: any) => new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime())
  .slice(0, 5);

const categories = [...new Set(allPosts.flatMap((post: any) => post.frontmatter.categories || []))];
---

<div class="sidebar-content">
  <!-- Bio Section -->
  <section class="bio-section">
    <div class="avatar-container">
      {config.author.avatar ? (
        <img src={config.author.avatar} alt={config.author.name} class="avatar" />
      ) : (
        <div class="avatar-placeholder">
          <svg viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Circular mask to contain the silhouette -->
            <defs>
              <clipPath id="circleClipSidebar">
                <circle cx="40" cy="40" r="39" />
              </clipPath>
            </defs>
            <g clip-path="url(#circleClipSidebar)">
              <!-- Head (oval shape) -->
              <ellipse cx="40" cy="28" rx="13" ry="15" fill="currentColor"/>
              <!-- Neck -->
              <path d="M33 40 Q33 44 35 46 L45 46 Q47 44 47 40 Z" fill="currentColor"/>
              <!-- Shoulders -->
              <path d="M35 46 Q28 48 22 54 Q16 60 14 70 L14 80 L66 80 L66 70 Q64 60 58 54 Q52 48 45 46 Z" fill="currentColor"/>
            </g>
          </svg>
        </div>
      )}
    </div>
    <h3 class="author-name">{config.author.name}</h3>
    <p class="bio-text">{config.author.bio}</p>
  </section>

  <!-- Recent Posts Section -->
  {recentPosts.length > 0 && (
    <section class="recent-posts">
      <h3 class="section-title">Recent Posts</h3>
      <ul class="post-list">
        {recentPosts.map(post => (
          <li class="post-item">
            <a href={`${base}/blog/${post.file.split('/').pop()?.replace(/\.(md|mdx)$/, '')}`} class="post-link">
              {post.frontmatter.title}
            </a>
            <time class="post-date" datetime={post.frontmatter.date}>
              {new Date(post.frontmatter.date).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric'
              })}
            </time>
          </li>
        ))}
      </ul>
    </section>
  )}

  <!-- Categories Section -->
  {categories.length > 0 && (
    <section class="categories">
      <h3 class="section-title">Categories</h3>
      <ul class="category-list">
        {categories.map(category => (
          <li class="category-item">
            <a href={`${base}/categories/${category.toLowerCase()}`} class="category-link">
              {category}
            </a>
          </li>
        ))}
      </ul>
    </section>
  )}
</div>

<style>
  .sidebar-content {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) * 4);
    padding: calc(var(--grid-unit) * 3);
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 4px;
  }

  .bio-section {
    text-align: center;
    padding-bottom: calc(var(--grid-unit) * 2);
    border-bottom: 1px solid var(--color-border);
  }

  .avatar-container {
    margin-bottom: calc(var(--grid-unit) * 2);
    display: flex;
    justify-content: center;
  }

  .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
    object-fit: cover;
  }

  .avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
    background-color: var(--color-light-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-medium-gray);
  }

  .author-name {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-accent);
    margin-bottom: calc(var(--grid-unit));
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .bio-text {
    font-size: 0.95rem;
    color: var(--color-text-muted);
    line-height: 1.5;
    margin: 0;
  }

  .section-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--color-accent);
    margin-bottom: calc(var(--grid-unit) * 1.5);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 2px solid var(--color-accent);
    padding-bottom: calc(var(--grid-unit) / 2);
  }

  .post-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) * 1.5);
  }

  .category-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) / 2);
  }

  .post-item {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) / 2);
  }

  .post-link {
    font-weight: 500;
    line-height: 1.3;
    color: var(--color-text-primary);
  }

  .post-link:hover,
  .post-link:focus {
    color: var(--color-accent);
  }

  .post-date {
    font-size: 0.85rem;
    color: var(--color-text-muted);
    font-family: var(--font-mono);
  }

  .category-item {
    display: inline-block;
  }

  .category-link {
    display: inline-block;
    padding: calc(var(--grid-unit) / 4) calc(var(--grid-unit) / 2);
    background-color: var(--color-text-primary);
    color: var(--color-background);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .category-link:hover,
  .category-link:focus {
    background-color: var(--color-accent);
    text-decoration: none;
  }

  /* Dark mode category link styles */
  :global([data-theme="dark"]) .category-link {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }

  :global([data-theme="dark"]) .category-link:hover,
  :global([data-theme="dark"]) .category-link:focus {
    background-color: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
  }

  @media (max-width: 1023px) {
    .sidebar-content {
      margin-bottom: calc(var(--grid-unit) * 3);
    }
  }
</style>