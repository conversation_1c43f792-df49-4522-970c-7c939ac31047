---
import type { Heading } from '../utils/table-of-contents';

interface Props {
  headings: Heading[];
}

const { headings } = Astro.props;
---

{headings.length > 0 && (
  <nav class="table-of-contents">
    <h2 class="toc-title">Table of Contents</h2>
    <ol class="toc-list">
      {headings.map(heading => (
        <li class={`toc-item toc-level-${heading.depth}`}>
          <a href={`#${heading.slug}`} class="toc-link">
            {heading.text}
          </a>
        </li>
      ))}
    </ol>
  </nav>
)}

<style>
  .table-of-contents {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: calc(var(--grid-unit) * 3);
    margin-bottom: calc(var(--grid-unit) * 4);
  }

  .toc-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: calc(var(--grid-unit) * 2);
    padding-bottom: calc(var(--grid-unit));
    border-bottom: 2px solid var(--color-accent);
  }

  .toc-list {
    margin: 0;
    padding: 0;
    list-style: none;
    counter-reset: toc-counter;
  }

  .toc-item {
    margin-bottom: calc(var(--grid-unit));
    counter-increment: toc-counter;
    position: relative;
  }

  .toc-item::before {
    content: counter(toc-counter) ".";
    position: absolute;
    left: 0;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--color-text-muted);
    font-weight: 600;
  }

  .toc-link {
    display: block;
    padding-left: calc(var(--grid-unit) * 3);
    color: var(--color-text-primary);
    text-decoration: none;
    line-height: 1.6;
    transition: all 0.2s ease;
  }

  .toc-link:hover,
  .toc-link:focus {
    color: var(--color-accent);
    transform: translateX(4px);
  }

  /* Indent for h3 items */
  .toc-level-3 {
    margin-left: calc(var(--grid-unit) * 2);
  }

  .toc-level-3 .toc-link {
    font-size: 0.95rem;
    color: var(--color-text-secondary);
  }

  .toc-level-3 .toc-link:hover,
  .toc-level-3 .toc-link:focus {
    color: var(--color-accent);
  }

  @media (max-width: 768px) {
    .table-of-contents {
      padding: calc(var(--grid-unit) * 2);
    }

    .toc-title {
      font-size: 1.1rem;
    }
  }
</style>

<script>
  // Add smooth scrolling and highlight active section
  document.addEventListener('DOMContentLoaded', () => {
    const tocLinks = document.querySelectorAll('.toc-link');
    
    tocLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href')?.slice(1);
        const targetElement = targetId ? document.getElementById(targetId) : null;
        
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          
          // Update URL without jumping
          history.pushState(null, '', `#${targetId}`);
        }
      });
    });

    // Highlight current section on scroll
    const observerOptions = {
      rootMargin: '-20% 0px -70% 0px'
    };

    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        const id = entry.target.getAttribute('id');
        const tocLink = document.querySelector(`.toc-link[href="#${id}"]`);
        
        if (tocLink) {
          if (entry.isIntersecting) {
            // Remove all active classes
            document.querySelectorAll('.toc-link').forEach(link => {
              link.classList.remove('active');
            });
            // Add active class to current link
            tocLink.classList.add('active');
          }
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all headings
    document.querySelectorAll('h2[id], h3[id]').forEach(heading => {
      observer.observe(heading);
    });
  });
</script>

<style>
  .toc-link.active {
    color: var(--color-accent);
    font-weight: 600;
  }

  .toc-link.active::after {
    content: "•";
    position: absolute;
    right: 0;
    color: var(--color-accent);
    font-size: 1.2rem;
  }
</style>