---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  canonical?: string;
}

import { SITE_TITLE, SITE_DESCRIPTION, SITE_URL } from '../config';

const {
  title = SITE_TITLE,
  description = SITE_DESCRIPTION,
  image = '/img/og-image.png',
  type = 'website',
  publishedTime,
  canonical,
} = Astro.props;

const pageTitle = title === SITE_TITLE ? title : `${title} | ${SITE_TITLE}`;
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const imageURL = SITE_URL ? SITE_URL + image : new URL(image, Astro.site);
---

<title>{pageTitle}</title>
<meta name="description" content={description} />
<meta name="author" content="Alieutica" />

<link rel="canonical" href={canonicalURL} />

<meta property="og:title" content={SITE_TITLE} />
<meta property="og:description" content={description} />
<meta property="og:type" content={type} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:image" content={imageURL} />
<meta property="og:site_name" content={SITE_TITLE} />
{publishedTime && <meta property="article:published_time" content={publishedTime} />}

<meta name="robots" content="index, follow" />
<meta name="googlebot" content="index, follow" />

<link rel="sitemap" type="application/xml" href="/sitemap-index.xml" />