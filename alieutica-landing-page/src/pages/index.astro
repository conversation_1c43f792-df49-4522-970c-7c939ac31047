---
import Layout from '../components/Layout.astro';
---

<Layout title="Alieutica - Expert Software Development Consulting" description="Expert software development consulting with 20+ years of C++/Python/JavaScript expertise across industrial, scientific, and academic contexts."
canonical="https://alieutica.com"
showSidebar={false}>
  <div class="landing-page">
    <header class="hero-section">
      <h1 class="hero-title">Expert Software Development</h1>
      <p class="hero-description">
        20+ years bridging industrial expertise with modern development.<br>
        Specializing in legacy system modernization, rapid prototyping,<br>
        and maintainable architecture for growing teams.
      </p>
      <hr class="hero-rule" />
    </header>

    <section class="experience-section">
      <h2 class="section-title">Two Decades of Cross-Platform Excellence</h2>
      <div class="experience-grid">
        <div class="experience-item">
          <h3>Multi-Language Mastery</h3>
          <p>C++/Python/JavaScript expertise across desktop, web, and scripting environments</p>
        </div>
        <div class="experience-item">
          <h3>Industrial & Scientific</h3>
          <p>Proven track record in industrial automation and scientific software development</p>
        </div>
        <div class="experience-item">
          <h3>Academic Collaboration</h3>
          <p>Research-to-production experience bridging academic innovation with commercial needs</p>
        </div>
        <div class="experience-item">
          <h3>Cross-Platform Architecture</h3>
          <p>Multiplatform solution design from embedded systems to cloud-native applications</p>
        </div>
      </div>
    </section>

    <section class="services-section">
      <h2 class="section-title">Core Services</h2>
      <div class="services-grid">
        <div class="service-card">
          <h3>Legacy System Modernization</h3>
          <ul>
            <li><strong>Industrial Focus:</strong> Transform aging C++ industrial systems to modern, maintainable code</li>
            <li><strong>Scientific Migration:</strong> Port academic research tools to production-ready applications</li>
            <li><strong>Platform Updates:</strong> Migrate desktop applications to web-based solutions</li>
            <li><strong>Performance Optimization:</strong> Enhance existing codebases for scalability</li>
          </ul>
        </div>

        <div class="service-card">
          <h3>Rapid Prototyping & MVPs</h3>
          <ul>
            <li><strong>Cross-Platform Development:</strong> Desktop and web solutions from single codebase</li>
            <li><strong>Scientific Tooling:</strong> Quick validation tools for research hypotheses</li>
            <li><strong>Industrial Automation:</strong> Prototype control and monitoring systems</li>
            <li><strong>API Development:</strong> Bridge legacy systems with modern interfaces</li>
          </ul>
        </div>

        <div class="service-card">
          <h3>Architecture & Code Maintenance</h3>
          <ul>
            <li><strong>Technical Debt Reduction:</strong> Systematic refactoring of complex codebases</li>
            <li><strong>Code Review & Quality:</strong> Establish maintainable coding standards</li>
            <li><strong>Documentation & Knowledge Transfer:</strong> Make systems understandable for teams</li>
            <li><strong>Performance Monitoring:</strong> Ongoing system health and optimization</li>
          </ul>
        </div>

        <div class="service-card">
          <h3>Domain-Specific Consulting</h3>
          <ul>
            <li><strong>Industrial Software:</strong> Manufacturing, automation, and control systems</li>
            <li><strong>Scientific Computing:</strong> Research tools, data analysis, and visualization</li>
            <li><strong>Academic Collaboration:</strong> Research-to-production software development</li>
            <li><strong>Cross-Domain Integration:</strong> Bridge different technical environments</li>
          </ul>
        </div>
      </div>
    </section>

    <section class="contact-section">
      <h2 class="section-title">Ready to Modernize Your Systems?</h2>
      <p class="contact-intro">
        Whether you're dealing with legacy industrial code, need rapid prototype development,<br>
        or want to scale your scientific software for production, let's discuss how<br>
        two decades of cross-platform expertise can help.
      </p>

      <div class="contact-content">
        <div class="contact-grid">
          <div class="contact-info">
            <h3>Let's Discuss Your Project</h3>
            <p>
              From legacy system modernization to rapid prototyping, we bring industrial-grade
              reliability with startup agility to solve complex technical challenges.
            </p>
            <p>Whether you're interested in software development, have questions about
              design, architecture or deployment, we'd love to hear from you.</p>

          <div class="contact-links">
            <a href="mailto:<EMAIL>" class="contact-link">
              <span class="contact-label">Email</span>
              <EMAIL>
            </a>

            <a href="https://github.com/alieutica" class="contact-link" target="_blank">
              <span class="contact-label">GitHub</span>
              @alieutica
            </a>
          </div>
        </div>

        <div class="contact-form-section">
          <h3>Project Discussion</h3>
          <form id="contactForm" class="contact-form">
            <div class="form-row">
              <div class="form-group">
                <label for="name" class="form-label">Name</label>
                <input type="text" id="name" name="name" class="form-input" required>
              </div>

              <div class="form-group">
                <label for="email" class="form-label">Email</label>
                <input type="email" id="email" name="email" class="form-input" required>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="projectType" class="form-label">Project Type</label>
                <select id="projectType" name="projectType" class="form-input" required>
                  <option value="">Select project type...</option>
                  <option value="legacy-modernization">Legacy Modernization</option>
                  <option value="rapid-prototyping">Rapid Prototyping</option>
                  <option value="code-review">Code Review & Architecture</option>
                  <option value="consulting">Technical Consulting</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div class="form-group">
                <label for="industryContext" class="form-label">Industry Context</label>
                <select id="industryContext" name="industryContext" class="form-input">
                  <option value="">Select context...</option>
                  <option value="industrial">Industrial/Manufacturing</option>
                  <option value="scientific">Scientific/Research</option>
                  <option value="academic">Academic</option>
                  <option value="startup">Startup</option>
                  <option value="enterprise">Enterprise</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div class="form-group">
              <label for="techStack" class="form-label">Current Technology Stack</label>
              <input type="text" id="techStack" name="techStack" class="form-input" placeholder="e.g., C++, Python, JavaScript, frameworks, databases...">
            </div>

            <div class="form-group">
              <label for="timeline" class="form-label">Project Timeline</label>
              <select id="timeline" name="timeline" class="form-input">
                <option value="">Select timeline...</option>
                <option value="urgent">Urgent (within 1 month)</option>
                <option value="short-term">Short-term (1-3 months)</option>
                <option value="medium-term">Medium-term (3-6 months)</option>
                <option value="long-term">Long-term partnership</option>
                <option value="flexible">Flexible</option>
              </select>
            </div>

            <div class="form-group">
              <label for="message" class="form-label">Project Details</label>
              <textarea id="message" name="message" class="form-textarea" rows="6" required placeholder="Please describe your project, current challenges, and what you're looking to achieve..."></textarea>
            </div>

            <button type="submit" class="form-submit" id="submitBtn">
              Send Message
            </button>
          </form>

          <div id="responseMessage" class="form-message"></div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<style>
  .landing-page {
    max-width: 1200px;
    margin: 0 auto;
  }

  /* Hero Section */
  .hero-section {
    text-align: center;
    margin-bottom: calc(var(--grid-unit) * 12);
  }

  .hero-title {
    font-family: var(--font-heading-primary);
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.1;
    margin-bottom: calc(var(--grid-unit) * 4);
  }

  .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 4);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-rule {
    height: 4px;
    background-color: var(--color-accent);
    border: none;
    width: 120px;
    margin: 0 auto;
  }

  /* Experience Section */
  .experience-section {
    margin-bottom: calc(var(--grid-unit) * 12);
  }

  .section-title {
    font-family: var(--font-heading-secondary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
    text-align: center;
    margin-bottom: calc(var(--grid-unit) * 8);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .experience-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: calc(var(--grid-unit) * 6);
    margin-bottom: calc(var(--grid-unit) * 8);
  }

  .experience-item h3 {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-accent);
    margin-bottom: calc(var(--grid-unit) * 2);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .experience-item p {
    line-height: 1.6;
    color: var(--color-text-secondary);
  }

  /* Services Section */
  .services-section {
    margin-bottom: calc(var(--grid-unit) * 12);
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: calc(var(--grid-unit) * 6);
  }

  .service-card {
    border: 2px solid var(--color-border);
    padding: calc(var(--grid-unit) * 4);
    background-color: var(--color-surface);
  }

  .service-card h3 {
    font-family: var(--font-heading-secondary);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-accent);
    margin-bottom: calc(var(--grid-unit) * 3);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .service-card ul {
    list-style: none;
    padding: 0;
  }

  .service-card li {
    margin-bottom: calc(var(--grid-unit) * 2);
    line-height: 1.6;
    color: var(--color-text-secondary);
  }

  .service-card li strong {
    color: var(--color-text-primary);
  }

  /* Contact Section */
  .contact-section {
    margin-bottom: calc(var(--grid-unit) * 8);
  }

  .contact-intro {
    text-align: center;
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 8);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .contact-content {
    line-height: 1.7;
  }

  .contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(var(--grid-unit) * 8);
    align-items: start;
  }

  .contact-info h2,
  .contact-form-section h2 {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: calc(var(--grid-unit) * 3);
    font-size: 1.5rem;
  }

  .contact-links {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) * 2);
    margin-top: calc(var(--grid-unit) * 4);
  }

  .contact-link {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    padding: calc(var(--grid-unit) * 2);
    border: 2px solid var(--color-border);
    transition: all 0.2s ease;
  }

  .contact-link:hover,
  .contact-link:focus {
    border-color: var(--color-accent);
    background-color: var(--color-surface);
  }

  .contact-label {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    color: var(--color-text-muted);
    margin-bottom: calc(var(--grid-unit) / 2);
  }

  .contact-link:hover .contact-label,
  .contact-link:focus .contact-label {
    color: var(--color-accent);
  }

  /* Form Styles */
  .contact-form {
    border: 2px solid var(--color-border);
    padding: calc(var(--grid-unit) * 4);
    background-color: var(--color-surface, #fafafa);
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: calc(var(--grid-unit) * 3);
  }

  .form-group {
    margin-bottom: calc(var(--grid-unit) * 3);
  }

  .form-label {
    display: block;
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    color: var(--color-text-primary);
    margin-bottom: calc(var(--grid-unit) * 1);
  }

  .form-input,
  .form-textarea {
    width: 100%;
    padding: calc(var(--grid-unit) * 2);
    border: 2px solid var(--color-border);
    background-color: var(--color-background, #ffffff);
    font-family: var(--font-body);
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color 0.2s ease;
    outline: none;
  }

  .form-input:focus,
  .form-textarea:focus {
    border-color: var(--color-accent);
  }

  .form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
  }

  .form-submit {
    background-color: var(--color-accent);
    color: var(--color-background, #ffffff);
    border: 2px solid var(--color-accent);
    padding: calc(var(--grid-unit) * 2) calc(var(--grid-unit) * 4);
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
  }

  .form-submit:hover:not(:disabled) {
    background-color: var(--color-background, #ffffff);
    color: var(--color-accent);
  }

  .form-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background-color: var(--color-text-muted);
    border-color: var(--color-text-muted);
    color: var(--color-background, #ffffff);
  }

  .form-message {
    margin-top: calc(var(--grid-unit) * 3);
    padding: calc(var(--grid-unit) * 2);
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    text-align: center;
    border: none;
    background: none;
    display: none;
  }

  .form-message.success {
    display: block;
    border: 2px solid var(--color-accent);
    color: var(--color-accent);
    background-color: var(--color-surface, #fafafa);
  }

  .form-message.error {
    display: block;
    border: 2px solid var(--color-accent);
    color: var(--color-accent);
    background-color: var(--color-surface, #fafafa);
  }

  .form-message-content {
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .form-message .form-message-button {
    background-color: var(--color-accent) !important;
    color: var(--color-background) !important;
    border: 2px solid var(--color-accent) !important;
    padding: calc(var(--grid-unit) * 1.5) calc(var(--grid-unit) * 4) !important;
    font-family: var(--font-heading-secondary) !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    font-size: 0.875rem !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    margin-top: calc(var(--grid-unit) * 2) !important;
    border-radius: 0 !important;
    min-width: 100px !important;
    display: inline-block !important;
    outline: none !important;
  }

  .form-message .form-message-button:hover {
    background-color: var(--color-background) !important;
    color: var(--color-accent) !important;
    transform: translateY(-1px) !important;
  }

  .form-message .form-message-button:active {
    transform: translateY(0) !important;
  }

  .loading-indicator {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-background, #ffffff);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-right: calc(var(--grid-unit) / 2);
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Dark mode text contrast fixes */
  @media (prefers-color-scheme: dark) {
    .contact-link {
      color: var(--color-text-primary);
    }

    .form-input,
    .form-textarea {
      color: var(--color-text-primary);
    }

    .form-input::placeholder,
    .form-textarea::placeholder {
      color: var(--color-text-muted);
    }
  }

  @media (max-width: 768px) {
    .page-title {
      font-size: 2.5rem;
    }

    .page-description {
      font-size: 1.125rem;
    }

    .contact-grid {
      grid-template-columns: 1fr;
      gap: calc(var(--grid-unit) * 6);
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 0;
    }

    .contact-form {
      padding: calc(var(--grid-unit) * 3);
    }
  }

  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.5rem;
    }

    .hero-description {
      font-size: 1.125rem;
    }

    .section-title {
      font-size: 2rem;
    }

    .experience-grid,
    .services-grid {
      grid-template-columns: 1fr;
      gap: calc(var(--grid-unit) * 4);
    }

    .contact-intro {
      font-size: 1.125rem;
    }

    .contact-grid {
      grid-template-columns: 1fr;
      gap: calc(var(--grid-unit) * 6);
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 0;
    }

    .contact-form {
      padding: calc(var(--grid-unit) * 3);
    }
  }

  @media (max-width: 480px) {
    .hero-title {
      font-size: 2rem;
    }

    .service-card {
      padding: calc(var(--grid-unit) * 3);
    }

    .contact-form {
      padding: calc(var(--grid-unit) * 2);
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm') as HTMLFormElement;
    const submitBtn = document.getElementById('submitBtn') as HTMLButtonElement;
    const responseMessage = document.getElementById('responseMessage') as HTMLDivElement;

    if (!form || !submitBtn || !responseMessage) return;

    form.addEventListener('submit', async function(e) {
      e.preventDefault();

      // Get form data
      const formData = new FormData(form);
      const data = {
        name: formData.get('name') as string,
        email: formData.get('email') as string,
        projectType: formData.get('projectType') as string,
        industryContext: formData.get('industryContext') as string,
        techStack: formData.get('techStack') as string,
        timeline: formData.get('timeline') as string,
        message: formData.get('message') as string
      };

      // Update button state
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<span class="loading-indicator"></span>Sending...';
      responseMessage.style.display = 'none';
      responseMessage.className = 'form-message';

      try {
        // Use configured API endpoint
        const response = await fetch(__CONTACT_API_URL__, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Site-Config': 'alieutica.com',
          },
          body: JSON.stringify(data)
        });

        if (response.ok) {
          // Show success message and keep form data
          responseMessage.innerHTML = `
            <div class="form-message-content">
              Thank You for Reaching Out<br>I'll review your project details and technical requirements, then respond within 24 hours with initial thoughts and next steps.
            </div>
            <button type="button" class="form-message-button" style="background-color: var(--color-accent) !important; color: var(--color-background) !important; border: 2px solid var(--color-accent) !important; padding: calc(var(--grid-unit) * 1.5) calc(var(--grid-unit) * 4) !important; font-family: var(--font-heading-secondary) !important; font-weight: 700 !important; text-transform: uppercase !important; letter-spacing: 0.05em !important; font-size: 0.875rem !important; cursor: pointer !important; transition: all 0.2s ease !important; margin-top: calc(var(--grid-unit) * 2) !important; border-radius: 0 !important; min-width: 100px !important; display: inline-block !important; outline: none !important;" onclick="this.closest('.form-message').style.display='none'; document.getElementById('contactForm').reset();">
              OK
            </button>
          `;
          responseMessage.className = 'form-message success';
          // CRITICAL: Ensure the message is visible by explicitly setting display
          responseMessage.style.display = 'block';

          // Reset button state but DON'T reset form yet
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Send Message';


          return; // Exit early to prevent any further processing
        } else {
          let errorMessage = 'Failed to send message';
          try {
            const responseText = await response.text();

            if (responseText.includes('<!doctype') || responseText.includes('<html')) {
              errorMessage = 'API endpoint not found. Please check your Worker deployment.';
            } else {
              try {
                const errorData = JSON.parse(responseText);
                errorMessage = errorData.error || errorMessage;
              } catch (jsonError) {
                console.warn('Failed to parse error response as JSON:', jsonError);
                errorMessage = `Server error: ${response.status} - ${responseText.substring(0, 100)}`;
              }
            }
          } catch (readError) {
            console.warn('Failed to read response text:', readError);
            errorMessage = `Server error: ${response.status}`;
          }
          throw new Error(errorMessage);
        }

      } catch (error) {
        console.error('Error:', error);
        let errorMessage = 'Error Sending Message<br>Please try again or contact us directly.';

        // More specific error messages
        if (error instanceof Error) {
          if (error.message.includes('API endpoint not found')) {
            errorMessage = 'Contact form not configured.<br>Please contact us directly via email.';
          } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Network error.<br>Please check your connection and try again.';
          } else {
            errorMessage = `Error: ${error.message}<br>Please try again or contact us directly.`;
          }
        }

        responseMessage.innerHTML = `
          <div class="form-message-content">
            ${errorMessage}
          </div>
          <button type="button" class="form-message-button" style="background-color: var(--color-accent) !important; color: var(--color-background) !important; border: 2px solid var(--color-accent) !important; padding: calc(var(--grid-unit) * 1.5) calc(var(--grid-unit) * 4) !important; font-family: var(--font-heading-secondary) !important; font-weight: 700 !important; text-transform: uppercase !important; letter-spacing: 0.05em !important; font-size: 0.875rem !important; cursor: pointer !important; transition: all 0.2s ease !important; margin-top: calc(var(--grid-unit) * 2) !important; border-radius: 0 !important; min-width: 100px !important; display: inline-block !important; outline: none !important;" onclick="this.closest('.form-message').style.display='none';">
            OK
          </button>
        `;
        responseMessage.className = 'form-message error';
        // CRITICAL: Ensure the error message is visible by explicitly setting display
        responseMessage.style.display = 'block';
      }

      // Reset button state
      submitBtn.disabled = false;
      submitBtn.innerHTML = 'Send Message';
    });

    // Enhance form accessibility and user experience
    const inputs = form.querySelectorAll('input, textarea');
    inputs.forEach(input => {
      input.addEventListener('focus', function(this: HTMLElement) {
        this.style.borderColor = 'var(--color-accent)';
      });

      input.addEventListener('blur', function(this: HTMLInputElement | HTMLTextAreaElement) {
        if (!this.value) {
          this.style.borderColor = 'var(--color-border)';
        }
      });
    });
  });
</script>
