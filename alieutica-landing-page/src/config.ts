export interface SiteConfig {
  title: string;
  description: string;
  author: {
    name: string;
    bio: string;
    avatar?: string;
  };
  social: {
    github?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    email?: string;
  };
  siteUrl: string;
}

export const config: SiteConfig = {
  title: "Alieutica",
  description: "Expert software development consulting. 20+ years of C++/Python/JavaScript expertise across industrial, scientific, and academic contexts. Specializing in legacy system modernization, rapid prototyping, and maintainable architecture.",
  author: {
    name: "<PERSON><PERSON><PERSON>",
    bio: "Expert software consultant with 20+ years bridging industrial, scientific, and academic domains with modern development practices.",
    // avatar: "/images/avatar.jpg" // Uncomment and add your avatar image to public/images/
  },
  social: {
    github: "https://github.com/alieutica",
    twitter: "https://twitter.com/alieutica",
    linkedin: "https://linkedin.com/in/alieutica",
    email: "<EMAIL>"
  },
  siteUrl: "https://alieutica.com"
};

// Export constants for SEO component
export const SITE_URL = config.siteUrl;
export const SITE_TITLE = config.title;
export const SITE_DESCRIPTION = config.description;