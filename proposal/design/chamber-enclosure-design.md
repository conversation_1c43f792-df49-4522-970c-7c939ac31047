# Chamber Control System Enclosure and EMI Design Guide
## Electrical Enclosure, Power Isolation, and EMI Shielding Specifications

### Table of Contents
1. [Overview](#overview)
2. [DIN Rail Component Layout](#din-rail-component-layout)
3. [Forced Air Cooling Design](#forced-air-cooling-design)
4. [Power and Signal Isolation](#power-and-signal-isolation)
5. [EMI Shielding for ADC Section](#emi-shielding-for-adc-section)
6. [Grounding and Shielding Strategy](#grounding-and-shielding-strategy)
7. [Cable Management and Routing](#cable-management-and-routing)
8. [Implementation Checklist](#implementation-checklist)

---

## 1. Overview

This guide provides detailed specifications for the physical implementation of the chamber control system, focusing on electromagnetic compatibility (EMC), thermal management, and signal integrity for high-precision 24-bit ADC measurements.

### Key Design Goals
- Maintain <1 LSB noise on 24-bit ADC
- Prevent ground loops and power coupling
- Ensure Raspberry Pi thermal stability
- Meet industrial EMC standards (EN 61326-1)

---

## 2. DIN Rail Component Layout

### 2.1 Enclosure Specifications
**Recommended Enclosure**: Rittal AE 1033.500
- Dimensions: 300 x 300 x 155 mm
- Material: Steel with conductive coating
- IP Rating: IP66
- DIN Rails: 2x 35mm rails (top and bottom)

### 2.2 DIN Rail Component Arrangement

```
Top DIN Rail (Power Components):
┌─────┬─────┬─────┬─────┬─────┬─────┐
│ CB  │ PS1 │ PS2 │ SSR │ EMI │ ISO │
│ 10A │ 12V │ 5V  │Heat │Filt│Xfmr │
└─────┴─────┴─────┴─────┴─────┴─────┘

Bottom DIN Rail (Control Components):
┌─────┬─────┬─────┬─────┬─────┬─────┐
│ RPi │ ADC │ PWM │ TC  │Relay│Term │
│Mount│Board│ HAT │ Amp │Board│Block│
└─────┴─────┴─────┴─────┴─────┴─────┘

Legend:
CB = Circuit Breaker
PS1/2 = Power Supplies
SSR = Solid State Relay
EMI Filt = EMI Filter
ISO Xfmr = Isolation Transformer
Term Block = Terminal Blocks
```

### 2.3 Recommended DIN Rail Components

| Component | Model | Purpose |
|-----------|-------|---------|
| Circuit Breaker | ABB S201-C10 | 10A overcurrent protection |
| 12V Supply | Mean Well HDR-60-12 | 60W DIN rail supply |
| 5V Supply | Mean Well HDR-30-5 | 30W DIN rail supply |
| SSR | Crydom DRA1-MP240D3 | Heater control |
| EMI Filter | Schaffner FN2090-10-06 | Line filtering |
| Isolation Transformer | Triad VPM24-2080 | 50VA, 1:1 isolation |
| RPi Mount | Phoenix Contact UM-35 | DIN rail adapter |
| Terminal Blocks | Phoenix Contact UT 2,5 | Wire termination |

---

## 3. Forced Air Cooling Design

### 3.1 Thermal Analysis
- Raspberry Pi 4: 7.5W max
- Power supplies: 15W dissipation
- Total heat load: ~25W
- Required air flow: 20 CFM minimum

### 3.2 Cooling System Design

```
Fan Placement (Side View):
┌─────────────────────────────┐
│ ←←← Intake Fan (filtered)   │
│ ┌─────────┐    ┌─────────┐ │
│ │   PS    │    │   RPi   │ │
│ │ Section │    │ Heatsink│ │
│ └─────────┘    └─────────┘ │
│              Exhaust Fan →→→ │
└─────────────────────────────┘

Air Flow Pattern:
- Diagonal flow across enclosure
- Cool air enters at bottom left
- Warm air exits at top right
```

### 3.3 Component Specifications

**Intake Fan**: Sunon MF40201V2-1000U-A99
- Size: 40x40x20mm
- Flow: 9.4 CFM
- Voltage: 12VDC
- Noise: 21 dBA
- Filter: 40mm washable foam filter

**Exhaust Fan**: Same as intake
- Mount with finger guard
- No filter required

**RPi Heatsink**: ICE Tower Cooling Fan
- Height: 40mm (low profile)
- 5V PWM controlled
- Direct GPIO connection

### 3.4 Temperature Monitoring

```python
# RPi CPU temperature monitoring
def get_cpu_temp():
    with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
        temp = float(f.read()) / 1000.0
    return temp

# Fan control based on temperature
def control_fans(temp):
    if temp > 70:  # °C
        set_fan_speed(100)  # Full speed
    elif temp > 60:
        set_fan_speed(70)   # 70% speed
    elif temp > 50:
        set_fan_speed(50)   # 50% speed
    else:
        set_fan_speed(30)   # Minimum speed
```

---

## 4. Power and Signal Isolation

### 4.1 Power Domain Separation

```
Mains Input → EMI Filter → Isolation Transformer
                                    ↓
                        ┌──────────┴──────────┐
                        │                     │
                    12V Supply            5V Supply
                        │                     │
                        ├─ Pump              ├─ RPi
                        ├─ Heater            ├─ ADC Board
                        └─ Fans              └─ Sensors

Three Isolated Domains:
1. Mains (AC) - Completely isolated
2. Power (12V) - Motors and heaters
3. Signal (5V) - Electronics only
```

### 4.2 Isolation Implementation

**Galvanic Isolation Points**:
1. **Mains Isolation**: 
   - Isolation transformer (3750V isolation)
   - Y-capacitors: 2x 4.7nF across barrier
   
2. **Digital Isolation** (if RS485 used):
   - ADM2682E isolated RS485 transceiver
   - 5kV RMS isolation rating
   
3. **Analog Isolation**:
   - ISO124 isolation amplifiers for critical signals
   - Alternative: Keep analog signals in same ground domain

### 4.3 Power Supply Filtering

```
Each Supply Output:
     ┌──────────────────────────┐
PS ──┼─ 100µF ─┬─ 10µF ─┬─ 0.1µF ├── Clean Power
     │ Electro │ Ceramic│ Ceramic│
     └─────────┴────────┴────────┘
                Common Ground Point

Additional Filtering:
- Ferrite beads on all power leads
- 0.1µF bypass caps at each IC
- Star ground configuration
```

### 4.4 Ground Domain Management

```
Ground Architecture:

EARTH ═══╦═══ Safety Ground (Enclosure)
         ║
    Isolation Gap
         ║
AGND ────╬─── Analog Ground (ADC, Sensors)
         │
DGND ────┴─── Digital Ground (RPi, Digital ICs)
         
Connected at single point only
```

---

## 5. EMI Shielding for ADC Section

### 5.1 Critical EMI Sources
- PWM signals (25 kHz pump drive)
- Switching power supplies
- RPi clock harmonics (1.5 GHz)
- External RF interference

### 5.2 ADC Shield Box Design

```
Custom Shield Enclosure:
┌─────────────────────────────┐
│  0.5mm Aluminum Box         │
│  ┌─────────────────────┐   │
│  │   ADS1256 Board     │   │
│  │                     │   │
│  │  [Filtered I/O]     │   │
│  └─────────────────────┘   │
│  Internal Dimensions:       │
│  100 x 80 x 30 mm          │
└─────────────────────────────┘

Features:
- Seamless aluminum construction
- Conductive gasket on lid
- Feedthrough capacitors for I/O
- Internal copper tape lining
```

### 5.3 I/O Filtering

**Analog Input Filtering**:
```
Sensor ──┬── 100Ω ──┬── 100Ω ──┬── To ADC
         │          │           │
        1nF      100nF         1nF
         │          │           │
        GND      Shield        AGND
        
Corner Frequency: ~16 kHz
Attenuation at 25 kHz: >20 dB
```

**Digital Signal Filtering**:
```
SPI Signals through shield:
- Use feedthrough capacitors (1000pF)
- Or ferrite beads + 100pF caps
- Keep SPI clock < 1 MHz inside shield
```

### 5.4 PCB Layout Considerations

**ADC Board Modifications**:
1. **Ground Plane**: Solid copper pour on bottom layer
2. **Guard Rings**: Around sensitive analog traces
3. **Kelvin Connections**: 4-wire sensing for precision
4. **Shielding**: Add copper tape over ADC chip

```
PCB Cross-section:
        Signal Trace
     ═══════════════
┌────────────────────┐ Top Layer
│////////////////////│ Ground Pour
├────────────────────┤ 
│                    │ Dielectric
├────────────────────┤
│████████████████████│ Bottom Ground
└────────────────────┘
     Guard Ring
```

---

## 6. Grounding and Shielding Strategy

### 6.1 Star Ground Implementation

```
Star Ground Physical Layout:
                 
         AGND Sources          Single Point Ground
              ↓                        ↓
    Sensors ──┤                   ┌───┬───┐
              │                   │   │   │
      ADC ────┼───────────────────┤ ⊛ │   │ 6mm² Bus
              │                   │   │   │
   TC Amp ────┤                   └───┴───┘
                                      │
    DGND ─────────────────────────────┘
    
⊛ = M4 Brass Stud (Star Point)
```

### 6.2 Cable Shield Termination

```
Correct Shield Termination:

Source End           Destination End
┌─────┐             ┌─────┐
│     ├─ Signal ────┤     │
│     ├─ Ground ────┤     │
│     │    ║        │     │
└─────┘    ║        └─────┘
      Shield connected
      at ONE end only
      (source end)
```

### 6.3 Sensor Cable Specifications

**Recommended Cable Types**:
| Signal Type | Cable Spec | Shield |
|-------------|------------|---------|
| Analog 0-5V | Belden 8451 | Foil + Braid |
| Thermocouple | Type K Extension | Individual foil |
| 4-20mA | Belden 9501 | Foil |
| Digital I²C | CAT5e STP | Overall foil |

---

## 7. Cable Management and Routing

### 7.1 Cable Segregation

```
Enclosure Cable Routing:
┌─────────────────────────┐
│ POWER CABLES →→→→→→→→→ │ Top Route
│ ┌─────────────────────┐ │
│ │                     │ │
│ │   Components        │ │ 50mm min
│ │                     │ │ separation
│ └─────────────────────┘ │
│ ←←←←←←← SIGNAL CABLES  │ Bottom Route
└─────────────────────────┘

Rules:
- Never parallel power and signal
- Cross at 90° if necessary
- Use cable trays for organization
```

### 7.2 Connector Panel Layout

```
Rear Panel Connectors:
┌─────────────────────────────┐
│  [AC In] [12V] [5V]   [USB] │
│                             │
│  ─────Power─────    ─Digital─│
│                             │
│  [CO2] [P] [T] [Flow] [Eth] │
│  ────Analog I/O────  ─Data──│
└─────────────────────────────┘

Use:
- IP67 rated connectors
- Separate power and signal
- EMI gaskets around openings
```

---

## 8. Implementation Checklist

### 8.1 Pre-Assembly
- [ ] Order EMI shielding materials
- [ ] Fabricate ADC shield box
- [ ] Prepare star ground bus
- [ ] Install DIN rails in enclosure
- [ ] Mount isolation transformer

### 8.2 Power Section
- [ ] Install EMI filter at entry
- [ ] Mount and wire power supplies
- [ ] Connect isolation transformer
- [ ] Install power distribution blocks
- [ ] Add output filtering capacitors

### 8.3 Control Section
- [ ] Mount RPi with heatsink
- [ ] Install ADC in shield box
- [ ] Connect filtered I/O
- [ ] Route SPI with shielded cable
- [ ] Implement star ground

### 8.4 Signal Wiring
- [ ] Use specified cable types
- [ ] Maintain power/signal separation
- [ ] Connect shields at source end only
- [ ] Add ferrite cores to cables
- [ ] Label all connections

### 8.5 Testing
- [ ] Verify ground isolation (>10MΩ)
- [ ] Check shield continuity (<0.1Ω)
- [ ] Measure ADC noise floor
- [ ] Test with PWM running
- [ ] Verify thermal performance

### 8.6 EMI Compliance Tests
- [ ] Conducted emissions (CISPR 11)
- [ ] Radiated emissions (30-1000 MHz)
- [ ] ESD immunity (IEC 61000-4-2)
- [ ] Burst immunity (IEC 61000-4-4)
- [ ] Surge immunity (IEC 61000-4-5)

---

## Appendix A: Recommended Suppliers

| Component | Supplier | Part Numbers |
|-----------|----------|--------------|
| EMI Gaskets | Laird | 8797-0100-54 |
| Feedthrough Caps | Spectrum Control | 51-033-7332 |
| Shield Boxes | Hammond | 1590Z100 |
| Ferrite Cores | Fair-Rite | 2643625002 |
| Ground Studs | Pomona | 3760-0 |
| Cable Glands | Hummel | 1.250.2001.50 |

## Appendix B: EMI Troubleshooting

**Common Issues and Solutions**:

| Symptom | Likely Cause | Solution |
|---------|--------------|----------|
| ADC noise spikes | PWM coupling | Add more filtering |
| Drift with temp | Ground loops | Check star ground |
| 50/60 Hz noise | Poor shielding | Verify shield integrity |
| Random glitches | ESD events | Add TVS diodes |
| High freq noise | Digital coupling | Increase separation |

---

*Document Version: 1.0*  
*Date: July 2025*  
*EMC Design: Technical Engineering Division*