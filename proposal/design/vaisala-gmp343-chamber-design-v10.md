# Vaisala GMP343 Measurement Chamber Design
## Complete Technical Specification and Implementation Guide

### Table of Contents
1. [System Overview](#system-overview)
2. [Component Specifications](#component-specifications)
3. [Chamber Design Details](#chamber-design-details)
4. [Flow System Design](#flow-system-design)
5. [Sensor Integration](#sensor-integration)
6. [Assembly Drawings](#assembly-drawings)
7. [Operating Procedures](#operating-procedures)
8. [Performance Specifications](#performance-specifications)

---

## 1. System Overview

The measurement chamber is designed to provide optimal gas sampling conditions for the Vaisala GMP343 CO₂ probe while incorporating pressure and temperature monitoring capabilities. The system uses a controlled flow configuration with upstream filtration and precise flow control.

### Key Features:
- Optimized 75 mL internal volume for fast response
- Integrated pressure and temperature monitoring
- Particle filtration to protect sensors
- Controlled flow rate via precision pump
- Modular design for easy maintenance

---

## 2. Component Specifications

### 2.1 Primary Sensor
**Vaisala GMP343 CO₂ Probe**
- Measurement range: 0-1000 ppm (standard)
- Accuracy: ±(5 ppm + 2% of reading)
- Response time (T63): <30 s (with grid filter)
- Response time (T90): <2 s (without grid filter, flow >5 m/s)
- Operating pressure: 700-1300 hPa
- Probe diameter: 18.5 mm
- Probe length: 155 mm
- Filter type: Sintered PTFE (removable for fast response)

### 2.2 Pressure Sensor
**NXP MPX5100AP**
- Pressure range: 0-100 kPa (gauge)
- Accuracy: ±2.5% FSS
- Output: 0.2-4.7 VDC (ratiometric)
- Response time: 1.0 ms
- Temperature compensated: 0-85°C
- Port style: Side-ported
- Supply voltage: 5.0 VDC

### 2.3 Temperature Sensor
**Omega Fine Wire Thermocouple (Type K)**
- Model: 5TC-TT-K-40-36
- Wire diameter: 0.003" (0.076 mm)
- Temperature range: -200 to 1250°C
- Accuracy: ±2.2°C or 0.75% (whichever is greater)
- Response time: <0.1 s (in air)
- Sheath: None (exposed junction)
- Lead length: 36"

### 2.4 Particle Filter
**Swagelok SS-4FW-2**
- Filter rating: 2 micron
- Maximum pressure: 6000 psig at 37°C
- Body material: 316 SS
- Element material: 316L SS sintered
- Connections: 1/4" Swagelok tube fittings
- Internal volume: 15.7 cm³
- Maximum temperature: 454°C

### 2.6 Flow Meter
**Sensirion SFM3000**
- Flow range: 0-10 L/min
- Accuracy: ±1.5% of reading
- Output: I²C digital
- Response time: 0.5 ms
- Temperature compensated
- Supply voltage: 5.0 VDC

### 2.7 Pressure Relief Valve
**Swagelok SS-RL3S4-S4**
- Set pressure: 20 kPa (3 psi)
- Cracking pressure: 17 kPa
- Flow capacity: >5 L/min at set pressure
- Connections: 1/4" Swagelok
- Body material: 316 SS

### 2.8 Buffer Volume
**Custom SS Vessel**
- Volume: 100 mL
- Material: 316 SS
- Ports: 2x 1/4" NPT
- Internal baffles for pulsation dampening

## 3. Chamber Design Details

### 3.1 Chamber Body Specifications

**Material**: 316 Stainless Steel
- Wall thickness: 3 mm
- Internal diameter: 40 mm
- Internal height: 60 mm
- Total internal volume: 75.4 mL

### 3.2 Port Specifications

**GMP343 Probe Port (Top)**
- Diameter: 19 mm (H7 tolerance)
- Seal: Double O-ring configuration
- O-ring material: Viton (FKM)
- O-ring size: AS568-018 (2x)
- Spacing: 10 mm between O-rings

**Gas Inlet Port**
- Size: 1/4" NPT female
- Location: Tangential entry at 30° downward angle
- Position: 20 mm from chamber bottom

**Gas Outlet Port**
- Size: 1/4" NPT female
- Location: Opposite inlet, 180° offset
- Position: 40 mm from chamber bottom

**Pressure Sensor Port**
- Size: 1/8" NPT female
- Location: 90° from inlet
- Position: 30 mm from chamber bottom

**Thermocouple Port**
- Type: Compression fitting for 1/16" probe
- Location: 270° from inlet
- Position: 25 mm from chamber bottom

### 3.3 Chamber Drawing

```
TOP VIEW:
     ┌─────────────────┐
     │                 │
     │    GMP343       │
     │    (⊕)          │
     │ P ←───────→ TC  │  P = Pressure port
     │       ↑         │  TC = Thermocouple
     │    Outlet       │
     │       ↓         │
     │    Inlet        │
     └─────────────────┘

SIDE VIEW (Section A-A):
     ┌─────┬─────┬─────┐
     │ ═══ │ ⊕ │ ═══ │ ← Double O-ring seal
     ├─────┴─────┴─────┤
     │                 │ ← 60 mm
     │    ←─OUT        │ ← 40 mm
     │                 │
     │ P─→      ←─TC   │ ← 30 mm
     │                 │ ← 25 mm
     │    IN→          │ ← 20 mm
     │                 │
     └─────────────────┘
         40 mm ID
```

---

## 4. Flow System Design

### 4.1 Flow Path Configuration

```
Ambient Air → Filter → Pump → Chamber → Exhaust
                ↓
         [SS-4FW-2]  [NMP830]  [Chamber]
```

### 4.2 Tubing Specifications
- Material: 316 SS or PTFE
- Size: 1/4" OD x 0.035" wall
- Fittings: Swagelok compression
- Maximum length: 1 meter total

### 4.3 Flow Rate Optimization
- Target flow rate: 1.0 L/min
- Reynolds number in chamber: ~2400 (turbulent)
- Residence time: 4.5 seconds
- Pressure drop: <5 kPa at target flow

### 4.4 Flow Control
- Method: PWM control of pump speed
- Control range: 0.5-2.0 L/min
- Feedback: Optional mass flow meter downstream

---

## 5. Sensor Integration

### 5.1 GMP343 Installation
1. Insert probe through top port until filter is centered in chamber
2. Ensure 15 mm clearance around filter element
3. Tighten compression nut to compress O-rings
4. Verify seal integrity with leak check

### 5.2 Pressure Sensor Mounting
1. Install MPX5100AP using 1/8" NPT adapter
2. Orient with ports horizontal to prevent moisture accumulation
3. Use PTFE tape on threads
4. Connect electrical leads per manufacturer pinout:
   - Pin 1: Vout
   - Pin 2: Ground
   - Pin 3: Vs (5V)

### 5.3 Thermocouple Installation
1. Insert through compression fitting until tip extends 10 mm into flow
2. Position downstream of GMP343 filter
3. Tighten fitting to secure without damaging wire
4. Connect to appropriate thermocouple reader/transmitter

---

## 6. Assembly Drawings

### 6.1 Exploded View

```
        GMP343 Probe
            ↓
    ┌───────────────┐
    │  Top Cap      │ ← O-rings (2x)
    ├───────────────┤
    │               │
    │  Chamber Body │ ← All sensor ports
    │               │
    └───────────────┘
         ↑    ↑
    Inlet  Outlet
```

### 6.2 Plumbing Diagram

```
                    ┌─────────┐
                    │ GMP343  │
                    │ Chamber │
    Air → Filter → Pump → IN  OUT → Exhaust
           ↓        ↓      ↓
      [SS-4FW-2] [NMP830] [P][T]
                           ↓  ↓
                      [MPX5100][TC]
```

### 6.3 Electrical Connections

```
Power Supply (12V)
    ├── NMP830KNDC-B Pump
    │   ├── (+) Red
    │   └── (-) Black
    │
    └── 5V Regulator → MPX5100AP
                        ├── Vs (Pin 3)
                        ├── GND (Pin 2)
                        └── Vout (Pin 1) → DAQ

GMP343 → RS-485 Interface → DAQ/Computer

Thermocouple → TC Reader/Transmitter → DAQ
```

---

## 7. Operating Procedures

### 7.1 Initial Setup
1. Assemble chamber with all sensors installed
2. Leak test at 50 kPa gauge pressure
3. Connect electrical interfaces
4. Verify sensor communications

### 7.2 Calibration
1. **Zero Gas Calibration**
   - Flow N₂ or CO₂-free air at 1 L/min
   - Allow 5 minutes stabilization
   - Record zero offset

2. **Span Gas Calibration**
   - Flow certified span gas (e.g., 400 ppm CO₂)
   - Allow 5 minutes stabilization
   - Adjust span factor

3. **Pressure/Temperature Verification**
   - Compare readings to reference instruments
   - Document any offsets

### 7.3 Routine Operation
1. Start pump at target flow rate
2. Monitor pressure (<10 kPa gauge typical)
3. Allow 2-3 minutes warm-up
4. Begin measurements
5. Log all parameters at desired interval

### 7.4 Maintenance Schedule
- **Daily**: Check flow rate and pressure
- **Weekly**: Inspect filter differential pressure
- **Monthly**: Clean chamber interior
- **Quarterly**: Replace filter element
- **Annually**: Recalibrate all sensors

---

## 8. Performance Specifications

### 8.1 System Performance
- **Response Time (T90)**: <2 seconds (without grid filter)
- **Measurement Range**: 0-1000 ppm CO₂
- **System Accuracy (Digital)**: ±(5 ppm + 2% of reading)
- **System Accuracy (Analog)**: ±(6.5 ppm + 2% of reading)
- **Operating Pressure**: Atmospheric ±50 kPa
- **Operating Temperature**: 0-50°C

### 8.2 Data Acquisition Requirements
- **GMP343**: RS-485 at 9600 baud
- **Pressure**: 0-5V analog input, 12-bit minimum
- **Temperature**: Type K thermocouple input
- **Sampling Rate**: 1 Hz minimum

### 8.3 High-Speed Data Acquisition (10 Hz)

**Challenge**: The GMP343 maximum output rate is 2 Hz via serial communication.

**Solution Architecture**:
1. **GMP343**: Poll at maximum 2 Hz rate
2. **Analog Sensors**: Sample at 10 Hz natively
   - MPX5100AP pressure sensor (analog)
   - Thermocouple via fast ADC
3. **Data Interpolation**: Use linear or spline interpolation for CO₂ values between measurements
4. **Alternative**: Use Vaisala GMP343 analog output (0-5V) for true 10 Hz sampling

**Recommended DAQ Configuration**:
```
GMP343 Options:
- Digital: RS-485 @ 2 Hz max → Interpolate to 10 Hz
- Analog: 0-5V output → Direct 10 Hz sampling

DAQ Hardware Requirements:
- Minimum 4 analog channels (CO₂, pressure, temperature, spare)
- 16-bit ADC resolution
- 100 Hz sampling capability per channel
- RS-485 interface for digital option
```

### 8.3 Power Requirements
- **Total System**: 15W maximum
- **Supply Voltage**: 12 VDC ±10%
- **Current Draw**: 1.2A peak (pump startup)

### 8.4 Environmental Conditions
- **Storage Temperature**: -20 to 70°C
- **Operating Humidity**: 0-95% RH (non-condensing)
- **Vibration**: Minimize for optimal performance

---

## 9. Temperature Control System

### 9.1 Active Temperature Control Design

**Heating System**:
- **Heater Type**: Silicone rubber heater pad (Omega SRFG-202/10)
  - Power: 20W at 12VDC
  - Size: 2" x 2" (50 x 50 mm)
  - Maximum temperature: 200°C
  - Adhesive backing for direct mounting

- **Temperature Controller**: PID controller (Omega CN16DPT-330)
  - Input: Type K thermocouple
  - Output: SSR drive, PWM
  - Accuracy: ±0.25% of span
  - Control modes: PID with auto-tune

**Thermal Design**:
```
Chamber Cross-Section with Heater:
     ┌─────────────────┐
     │   Insulation    │ ← 25mm closed-cell foam
     ├─────────────────┤
     │ ═══════════════ │ ← Heater pad
     │                 │
     │  Chamber Body   │ ← Temperature setpoint: 35°C ±0.5°C
     │                 │
     └─────────────────┘
```

**Installation**:
1. Apply heater pad to bottom 1/3 of chamber exterior
2. Install RTD or second thermocouple for control feedback
3. Wrap entire assembly in thermal insulation
4. Maintain chamber 5-10°C above dew point

### 9.2 Passive Temperature Management

**Pre-conditioning Coil**:
- 1.5 m of 1/4" SS tubing coiled around chamber
- Thermal equilibration before entry
- Reduces temperature shock to sensors

---

## 10. Moisture Management System

### 10.1 Nafion Dryer Integration

**Recommended Component**: Perma Pure MD-050-12S-2
- Removes water vapor selectively
- No CO₂ loss
- Flow rate: up to 2 L/min
- Dew point reduction: -20°C typical

**Integration**:
```
Flow Path with Moisture Control:
Air → Filter → Nafion → Pump → Chamber
                 ↓
          [Dry Purge Air]
```

### 10.2 Condensation Trap Design

**Coalescing Filter/Trap**:
- **Model**: Parker Balston 95S6
- **Efficiency**: 99.99% at 0.01 micron
- **Drainage**: Manual or automatic
- **Location**: Between pump and chamber

**Custom Trap Design** (Alternative):
```
     IN →
    ┌────┐
    │    │← Cooling zone (optional)
    │    │
    │ ∪∪ │← Baffles
    │    │
    │~~~~│← Collected condensate
    └────┘
      ↓ Drain valve
    → OUT (to chamber)
```

**Design Features**:
- Volume: 50 mL
- Material: 316 SS or glass
- Baffles: Create turbulence for droplet coalescence
- Drain: 1/8" valve at bottom
- Operating principle: Gravity separation + impaction

### 10.3 Dew Point Monitoring

**Add Dew Point Sensor**: Vaisala DMT143
- Range: -60 to +60°C Td
- Accuracy: ±2°C
- Output: 4-20 mA
- Integration: Mount upstream of chamber

**Control Logic**:
```python
if (dew_point + 5°C) > chamber_temp:
    activate_heater()
    increase_setpoint()
else:
    normal_operation()
```

---

## 11. Data Acquisition Optimization for 10 Hz

### 11.1 Hardware Architecture

**Primary Option - Hybrid Approach**:
1. **CO₂ via Analog**: Configure GMP343 for 0-5V output
   - True 10 Hz sampling possible
   - Slight accuracy trade-off vs. digital
   
2. **High-Speed DAQ**: NI USB-6009 or similar
   - 48 kS/s aggregate
   - 14-bit resolution
   - 8 analog inputs

**Wiring Configuration**:
```
GMP343 Analog Out ──→ AI0 ─┐
MPX5100AP ─────────→ AI1  │
TC Amplifier ──────→ AI2  ├→ USB DAQ → PC
Dew Point ─────────→ AI3  │
                          ─┘
```

### 11.2 Software Implementation

**Data Collection Strategy**:
```python
# Pseudo-code for 10 Hz acquisition
import nidaqmx
import numpy as np

# Configure tasks
with nidaqmx.Task() as task:
    # Add channels
    task.ai_channels.add_ai_voltage_chan("Dev1/ai0:3")
    
    # Configure timing - 10 Hz continuous
    task.timing.cfg_samp_clk_timing(
        rate=10,
        sample_mode=AcquisitionType.CONTINUOUS
    )
    
    # Read loop
    while True:
        data = task.read(number_of_samples=1)
        # Process: CO2, Pressure, Temp, Dew Point
        co2_ppm = data[0] * 200  # 0-5V = 0-1000ppm
        pressure_kpa = data[1] * 20  # 0-5V = 0-100kPa
        temp_c = tc_linearize(data[2])
        dewpoint = (data[3] - 4) * 7.5 - 60  # 4-20mA
```

### 11.3 Alternative - Smart Interpolation

**For Digital RS-485 at 2 Hz**:
```python
# Cubic spline interpolation between points
from scipy.interpolate import interp1d

# Collect 2 Hz data
co2_times = [0, 0.5, 1.0, 1.5]  # seconds
co2_values = [400, 402, 401, 403]  # ppm

# Create interpolator
f = interp1d(co2_times, co2_values, kind='cubic')

# Generate 10 Hz points
time_10hz = np.arange(0, 2, 0.1)
co2_10hz = f(time_10hz)
```

**Advantages**:
- Maintains digital accuracy
- Smooth transitions
- Works with existing RS-485

---

| Item | Description | Part Number | Quantity |
|------|-------------|-------------|----------|
| 1 | CO₂ Sensor | Vaisala GMP343 | 1 |
| 2 | Pressure Sensor | NXP MPX5100AP | 1 |
| 3 | Thermocouple | Omega 5TC-TT-K-40-36 | 1 |
| 4 | Particle Filter | Swagelok SS-4FW-2 | 1 |
| 5 | Pump | KNF NMP830KNDC-B | 1 |
| 6 | Chamber Body | Custom SS-316 | 1 |
| 7 | O-rings | AS568-018 Viton | 2 |
| 8 | Tubing | 1/4" SS-316 | 2 m |
| 9 | Fittings | Various Swagelok | 10 |
| 10 | Power Supply | 12V 2A | 1 |

## Appendix B: Troubleshooting Guide

| Symptom | Possible Cause | Solution |
|---------|----------------|----------|
| No CO₂ reading | Power/communication issue | Check RS-485 connection |
| Slow response | Low flow rate | Increase pump speed |
| Pressure fluctuation | Blocked filter | Replace filter element |
| Temperature drift | Poor thermal contact | Reposition thermocouple |
| High pressure | Outlet restriction | Check exhaust line |

---

## Appendix E: Signal Path Analysis and ADC Considerations

### E.1 Complete Signal Chain Understanding

**Digital Output Path (RS-485)**:
```
CO₂ Sensor → Internal A/D → Digital Processing → RS-485 Output → Host System
(NDIR)       (Proprietary)   (Compensation)      (2 Hz max)
```

**Analog Output Path (0-5V)**:
```
CO₂ Sensor → Internal A/D → Digital Processing → Internal D/A → 0-5V Output → TI ADS1256 → Digital Data
(NDIR)       (Proprietary)   (Compensation)       (12-14 bit)    (±1.5 ppm)    (24-bit)      (10 Hz)
```

### E.2 Resolution and Accuracy Limitations

**Key Understanding**: The TI ADS1256's 24-bit resolution cannot recover information lost in the GMP343's internal D/A conversion.

**Resolution Analysis**:
1. **GMP343 Internal D/A**: 
   - Typical resolution: 12-14 bits
   - Over 1000 ppm range: 0.24-0.06 ppm steps
   - Accuracy: ±0.15% FS (±1.5 ppm)

2. **TI ADS1256 ADC**:
   - Resolution: 24-bit (19 bits effective @ 10 Hz)
   - Can resolve: 0.0019 ppm theoretical
   - **But limited by** the analog signal's inherent quantization

**Practical Implication**: The ADS1256 provides excellent noise performance and synchronization but cannot improve upon the ±1.5 ppm uncertainty introduced by the analog output.

### E.3 Why Direct Access Is Not Possible

The GMP343's internal signal processing cannot be bypassed because:

1. **Proprietary NDIR Algorithm**: 
   - Temperature compensation
   - Pressure compensation  
   - Optical path corrections
   - Aging compensation

2. **Calibration Data**:
   - Factory calibration coefficients
   - Stored internally and applied digitally

3. **Signal Conditioning**:
   - Lock-in amplification
   - Digital filtering
   - All happens before any accessible output

### E.4 Recommended Approach for 10 Hz Requirements

**Option 1: Analog Output with Understanding**
- Accept ±6.5 ppm + 2% accuracy
- Benefit from true 10 Hz temporal resolution
- Ideal for tracking rapid changes

**Option 2: Hybrid Approach**
```python
# Use RS-485 for absolute accuracy (every 30 seconds)
accurate_value = read_rs485()  # ±5 ppm + 2%

# Use analog for high-speed trends
for i in range(300):  # 30 seconds @ 10 Hz
    analog_trend[i] = read_analog()  # 10 Hz
    
# Apply correction factor
correction = accurate_value - analog_trend[-1]
corrected_data = analog_trend + correction
```

**Option 3: Accept 2 Hz with Interpolation**
- Maintain best accuracy
- Use sophisticated interpolation
- Suitable if CO₂ changes are gradual

### E.5 ADS1256 Benefits Despite Limitations

The TI ADS1256 still provides significant advantages:

1. **Synchronization**: All sensors sampled simultaneously
2. **Noise Reduction**: Oversampling and digital filtering
3. **Dynamic Range**: Better utilization of the 0-5V span
4. **Flexibility**: Programmable gain for other sensors
5. **Stability**: Superior to typical 12-bit DAQ cards

**Conclusion**: While the ADS1256 cannot overcome the GMP343's analog output limitations, it remains the optimal choice for high-quality, synchronized, multi-sensor data acquisition at 10 Hz.

---

## Appendix F: Response Time Physics and Flow Dynamics

### F.1 Response Time Specifications and Sources

**Source**: Vaisala GMP343 technical documentation and application notes

| Configuration | T63 | T90 | Flow Requirement |
|--------------|-----|-----|------------------|
| With grid filter | <30 s | <50 s | Minimal (diffusion mode) |
| Without grid filter | <1 s | <2 s | >5 m/s across sensor |

**Note**: T63 ≈ T90/2.3 for first-order exponential response systems

### F.2 Measurement Physics Comparison

**With Grid Filter - Diffusion Mode**:
```
   ┌─────────────┐
   │ PTFE Filter │ ← Sintered barrier (50-100 μm pores)
   │ ▓▓▓▓▓▓▓▓▓▓ │
   ├─────────────┤
   │             │ ← CO₂ diffuses through filter
   │  IR Cavity  │ ← Concentration equilibrates slowly
   │             │
   └─────────────┘

Physics: Fick's law of diffusion
Response time: τ ≈ L²/D
Where: L = filter thickness (~2mm)
       D = CO₂ diffusivity (~16 mm²/s in air)
```

**Without Grid Filter - Convection Mode**:
```
   ┌─────────────┐
   │   Open      │ ← Direct exposure to flow
   │      ↓      │
   │  ┌─────┐   │
   │  │ IR  │←←← │ ← Forced convection (5-10 m/s)
   │  └─────┘    │
   └─────────────┘

Physics: Convective mass transfer
Response time: τ ≈ V/Q
Where: V = cavity volume (~0.5 cm³)
       Q = volume exchange rate
```

### F.3 Flow Velocity Requirements

**Why High Velocity is Critical Without Filter**:

1. **Boundary Layer Disruption**: 
   - Static boundary layer forms at sensor surface
   - Thickness δ ≈ 5mm at low velocities
   - High velocity reduces to δ < 1mm

2. **Cavity Flushing**:
   - IR measurement cavity has ~0.5 cm³ dead volume
   - Requires 5-10 volume exchanges for 90% response
   - Time = (0.5 cm³ × 10) / flow rate

3. **Mass Transfer Coefficient**:
   - Sherwood number: Sh = hL/D ≈ 0.664 Re^0.5 Sc^0.33
   - Higher velocity → Higher Re → Better mass transfer

### F.4 Flow Calculations for Chamber Design

**Direct Flow Approach**:
```
Sensor aperture area: ~1 cm²
Required velocity: 5-10 m/s
Total flow needed: 3-6 L/min

Challenge: Requires high pump capacity
```

**Optimized Nozzle Design**:
```
Nozzle Configuration:
    ┌─────────────────┐
    │     GMP343      │
    │    [sensor]     │
    ├─────────────────┤
    │   ←←←←←←←←←    │ ← Jet impingement
    │   ┌─╲___╱─┐    │   Nozzle: 6mm diameter
IN ━━━━▶│   \_/   │━━━▶ OUT
    │   └─────────┘   │
    └─────────────────┘

Calculations:
- Nozzle diameter: 6 mm
- Nozzle area: 0.283 cm²
- For 5 m/s: Q = 0.85 L/min
- For 10 m/s: Q = 1.7 L/min
- Pump setting: 1.5-2.0 L/min optimal
```

### F.5 Protection Requirements Without Filter

**Critical Considerations**:

1. **Particle Protection**:
   - 2 μm upstream filter is MANDATORY
   - Any particles reaching IR cavity cause permanent drift
   - Consider dual-stage filtration for critical applications

2. **Condensation Prevention**:
   - Maintain chamber temp > dew point + 5°C
   - Pre-heat incoming air if necessary
   - Monitor humidity continuously

3. **Pressure Fluctuations**:
   - High velocity can cause turbulent pressure variations
   - Install flow straightener upstream of nozzle
   - Consider pulsation dampener after pump

**Recommended Flow Straightener**:
```
   ┌─────────────┐
   │ ┊┊┊┊┊┊┊┊┊┊ │ ← Bundle of 3mm tubes
   │ ┊┊┊┊┊┊┊┊┊┊ │   Length: 30mm
   │ ┊┊┊┊┊┊┊┊┊┊ │   Or: Honeycomb insert
   └─────────────┘
```

### F.6 Performance Validation

**Testing Protocol**:
1. Inject step change in CO₂ (e.g., 400 → 500 ppm)
2. Record response at 10 Hz
3. Calculate T63 and T90 from data
4. Verify T90 < 2 seconds

**Expected Results**:
- T10-90: 1.5-2.0 seconds (properly designed)
- Noise: <1 ppm RMS at 10 Hz
- Overshoot: <5% with proper flow control

---

## Appendix G: Flow Control System Design

### G.1 Complete Flow Control Architecture

```
┌─────────────────────────────────────────────────────────────────────┐
│                        Flow Control System                           │
├─────────────────────────────────────────────────────────────────────┤
│                                                                     │
│  Air → Filter → Pump → Buffer → Flow → Relief → Flow    → Chamber  │
│                        Volume   Meter  Valve   Straight.           │
│         ↓        ↓       ↓       ↓      ↓        ↓                │
│    [SS-4FW-2] [NMP830] [100mL] [SFM]  [PRV]  [Honey.]            │
│                  ↑               ↓                                  │
│                  └── PWM ←── Controller                            │
│                              (PID)                                  │
└─────────────────────────────────────────────────────────────────────┘
```

### G.2 Flow Measurement and Control

**Flow Meter Integration**:
- Model: Sensirion SFM3000
- Position: After buffer volume, before chamber
- Communication: I²C to microcontroller
- Update rate: 100 Hz (averaged to 10 Hz)

**PWM Pump Control Circuit**:
```
Microcontroller          MOSFET Driver         Pump
(Arduino/STM32)          (IRL540N)            (KNF NMP830)
    ┌───┐               ┌───┐                ┌───┐
PWM │   │──────────────│ G │                │ M │
Pin │   │          ┌───│ D ├────────────────┤   │
    └───┘          │   │ S │                └───┘
                   │   └───┘
                  ─┴─ GND   +12V ────────────┘
                   
PWM Frequency: 25 kHz (ultrasonic)
Duty Cycle: 20-100% (0.4-3.0 L/min)
```

### G.3 Bypass Flow Configuration

**Design Rationale**: Maintains constant pump speed for stability while allowing fine flow control

```
                          ┌─────────────┐
                    ┌─────┤   Chamber   ├─────┐
                    │     └─────────────┘     │
                    │      (1.5 L/min)        │
Filter → Pump ──────┤                         ├──── Exhaust
     (3 L/min)      │     ┌─────────────┐     │
                    └─────┤ Needle Valve├─────┘
                          └─────────────┘
                           (1.5 L/min)
                           
Needle Valve: Swagelok SS-1RS4
Adjustment: Manual or motorized
```

### G.4 Pressure Management

**Relief Valve Sizing**:
- Normal operating pressure: 5-10 kPa
- Relief set point: 20 kPa
- Maximum transient: 25 kPa

**Pressure Monitoring Points**:
1. After pump (high pressure side)
2. In chamber (via MPX5100AP)
3. Optional: Filter differential pressure

### G.5 Flow Conditioning Components

**Buffer Volume Design**:
```
     100 mL Volume
    ┌─────────────┐
IN →│  ┌───┐     │
    │  │   │     │ ← Internal baffle
    │  │   │     │   (reduces pulsations)
    │  └───┘     │→ OUT
    └─────────────┘
    
Material: 316 SS
Ports: 1/4" NPT
Time constant: ~2 seconds
```

**Flow Straightener**:
```
    ┌─────────────┐
    │ ┊┊┊┊┊┊┊┊┊┊ │ ← 19x 3mm tubes
    │ ┊┊┊┊┊┊┊┊┊┊ │   Length: 30mm
    │ ┊┊┊┊┊┊┊┊┊┊ │   Or: Honeycomb
    └─────────────┘
    
Reynolds number in tubes: <500 (laminar)
Pressure drop: <0.5 kPa
```

### G.6 Control Software Implementation

**PID Flow Controller**:
```python
import time
from sensirion_i2c_driver import SensirionI2cConnection
from sensirion_i2c_sfm import Sfm3000

class FlowControlSystem:
    def __init__(self):
        # Initialize hardware
        self.flow_meter = Sfm3000(SensirionI2cConnection(1))
        self.flow_meter.start_continuous_measurement()
        
        # PID parameters
        self.setpoint = 1.5  # L/min
        self.Kp = 0.5
        self.Ki = 0.1
        self.Kd = 0.05
        
        # Control variables
        self.integral = 0
        self.last_error = 0
        self.last_time = time.time()
        
        # Safety limits
        self.min_flow = 0.5  # L/min
        self.max_pressure = 25  # kPa
        self.min_temp_above_dewpoint = 3  # °C
        
    def update(self):
        # Read sensors
        flow = self.read_flow()
        pressure = self.read_pressure()
        temp = self.read_temperature()
        dewpoint = self.read_dewpoint()
        
        # Safety checks
        if not self.safety_check(flow, pressure, temp, dewpoint):
            self.emergency_stop()
            return
            
        # PID calculation
        current_time = time.time()
        dt = current_time - self.last_time
        
        error = self.setpoint - flow
        self.integral += error * dt
        derivative = (error - self.last_error) / dt
        
        # Anti-windup
        self.integral = max(-10, min(10, self.integral))
        
        # Calculate output
        output = (self.Kp * error + 
                 self.Ki * self.integral + 
                 self.Kd * derivative)
        
        # Convert to PWM duty cycle (20-100%)
        pwm_duty = max(20, min(100, 50 + output * 20))
        self.set_pump_pwm(pwm_duty)
        
        # Update state
        self.last_error = error
        self.last_time = current_time
        
    def safety_check(self, flow, pressure, temp, dewpoint):
        if flow < self.min_flow:
            print(f"WARNING: Low flow {flow} L/min - check filter")
            
        if pressure > self.max_pressure:
            print(f"ERROR: High pressure {pressure} kPa")
            return False
            
        if temp < dewpoint + self.min_temp_above_dewpoint:
            print(f"ERROR: Condensation risk - temp {temp}°C, dewpoint {dewpoint}°C")
            return False
            
        return True
        
    def emergency_stop(self):
        self.set_pump_pwm(0)
        print("EMERGENCY STOP - System shutdown")
```

### G.7 Startup and Shutdown Procedures

**Startup Sequence**:
1. Check all sensors online
2. Verify chamber temperature > dewpoint + 5°C
3. Start pump at 50% PWM
4. Wait for flow stabilization (10 seconds)
5. Enable PID control
6. Ramp to setpoint over 30 seconds

**Shutdown Sequence**:
1. Ramp down flow over 30 seconds
2. Continue pump at 20% for 60 seconds (purge)
3. Stop pump
4. Log final sensor readings

### G.8 Fault Detection and Alarms

| Condition | Threshold | Action |
|-----------|-----------|---------|
| Low flow | <0.5 L/min | Warning + Check filter |
| High pressure | >25 kPa | Emergency stop |
| Filter loading | ΔP >10 kPa | Warning + Schedule maintenance |
| Condensation risk | T < Td+3°C | Stop + Activate heater |
| Flow instability | σ >0.2 L/min | Check pump/tubing |
| Sensor fault | No response | Stop + Alert operator |

---

## Appendix H: Raspberry Pi Integration and Interface Design

### H.1 System Architecture with Raspberry Pi

```
┌─────────────────────────────────────────────────────────────────┐
│                    Raspberry Pi 4 Control System                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                  │
│  ┌─────────────┐     ┌──────────────┐     ┌─────────────────┐ │
│  │   RPi 4B    │     │ AD/DA Board  │     │  Thermocouple   │ │
│  │             │ SPI │  (ADS1256)   │     │   Amplifier     │ │
│  │          GPIO────▶│              │ SPI │  (MAX31855)     │ │
│  │             │     │ 8 Analog In  │─────┤                 │ │
│  │          I²C────┐ └──────────────┘     └─────────────────┘ │
│  │             │   │                                            │
│  └─────────────┘   │ ┌──────────────┐     ┌─────────────────┐ │
│                    └▶│ PWM/Servo HAT│     │   Flow Meter    │ │
│                      │  (PCA9685)   │ I²C │  (SFM3000)      │ │
│                      │              │─────┤                 │ │
│                      └──────────────┘     └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### H.2 GPIO Pin Assignments

| GPIO | Function | Connection |
|------|----------|------------|
| 2,3 | I²C SDA/SCL | PWM HAT, Flow meter |
| 8,10,11 | SPI0 | ADS1256 ADC |
| 17 | Interrupt | ADS1256 DRDY |
| 18 | PWM (backup) | Direct pump control |
| 22,23,24 | SPI1 | MAX31855 TC amp |
| 27 | Digital Out | Emergency stop relay |

### H.3 Sensor Interface Specifications

**1. GMP343 CO₂ Sensor (Analog Only)**
```
Analog Output Configuration:
- Output: 0-5V (0-1000 ppm)
- Update rate: >10 Hz capability
- Connection: Direct to ADS1256 AIN0

Wiring:
GMP343 Analog Out (Green) ──┬── 10kΩ ──┬── ADS1256 AIN0
                            │          │
                           10kΩ       0.1µF
                            │          │
                           GND        GND

Power Supply:
GMP343 Brown (+) → +24V DC
GMP343 Blue (-) → GND
GMP343 Shield → Earth GND
```

**2. All Analog Inputs (ADS1256)**
```
Channel assignments:
AIN0: GMP343 CO₂ (0-5V) → 2:1 divider → 0-2.5V
AIN1: MPX5100AP pressure (0.2-4.7V) → 2:1 divider → 0.1-2.35V
AIN2: Dew point sensor (4-20mA) → 250Ω shunt → 1-5V → 2:1 → 0.5-2.5V
AIN3: Spare analog input
AIN4-7: Available for expansion

Input protection circuit (all channels):
Sensor ──┬── 10kΩ ──┬── AINx
         │          │
        10kΩ      0.1µF
         │          │
        GND        GND
```

**3. Thermocouple (MAX31855)**
```python
# SPI Configuration
CS Pin: GPIO24
SPI: SPI1 (GPIO23=MOSI, GPIO22=SCLK)
Speed: 5 MHz max

# Direct connection
Type K TC → MAX31855 → RPi SPI1
Cold junction: Internal compensation
```

**4. Flow Meter (I²C)**
```python
# Sensirion SFM3000
I²C Address: 0x40
Bus: I²C-1 (GPIO2/3)
Pull-ups: 4.7kΩ to 3.3V (on-board)
```

**5. PWM Outputs (PCA9685)**
```
Channel 0: Pump PWM (via MOSFET driver)
Channel 1: Heater PWM (via SSR)
Channel 2-15: Spare

MOSFET Driver Circuit:
PCA9685 Ch0 ──┬── 1kΩ ──┬── IRLZ44N Gate
              │         │
             10kΩ      └── 12V Pump -
              │
             GND         12V Pump + → +12V
```

### H.4 Software Stack

**Operating System Configuration**:
```bash
# Enable interfaces in /boot/config.txt
dtparam=i2c_arm=on
dtparam=spi=on

# Install required packages
sudo apt-get update
sudo apt-get install python3-pip python3-dev
sudo apt-get install python3-smbus i2c-tools
sudo apt-get install python3-spidev python3-rpi.gpio

# Python libraries
pip3 install adafruit-circuitpython-ads1256
pip3 install adafruit-circuitpython-pca9685
pip3 install adafruit-circuitpython-max31855
pip3 install sensirion-i2c-sfm
```

### H.5 Integrated Control Software (Analog Only)

```python
#!/usr/bin/env python3
"""
Vaisala GMP343 Chamber Control System for Raspberry Pi
Version 2.0 - Analog acquisition only
"""

import time
import board
import busio
import digitalio
import adafruit_ads1256
import adafruit_pca9685
import adafruit_max31855
from sensirion_i2c_driver import I2cConnection
from sensirion_i2c_sfm import Sfm3000
import numpy as np

class ChamberController:
    def __init__(self):
        # Initialize I²C bus
        self.i2c = busio.I2C(board.SCL, board.SDA)
        
        # Initialize SPI buses
        self.spi0 = busio.SPI(board.SCLK, MOSI=board.MOSI, MISO=board.MISO)
        self.spi1 = busio.SPI(board.D23, MOSI=board.D22, MISO=board.D21)
        
        # Initialize ADS1256 ADC
        cs_adc = digitalio.DigitalInOut(board.CE0)
        self.adc = adafruit_ads1256.ADS1256(self.spi0, cs_adc)
        self.adc.data_rate = adafruit_ads1256.DataRate.RATE_100
        
        # Initialize MAX31855 thermocouple
        cs_tc = digitalio.DigitalInOut(board.D24)
        self.thermocouple = adafruit_max31855.MAX31855(self.spi1, cs_tc)
        
        # Initialize PCA9685 PWM controller
        self.pwm = adafruit_pca9685.PCA9685(self.i2c)
        self.pwm.frequency = 25000  # 25 kHz for pump
        
        # Initialize flow meter
        self.flow_meter = Sfm3000(I2cConnection(1))
        self.flow_meter.start_continuous_measurement()
        
        # Emergency stop GPIO
        self.emergency_stop = digitalio.DigitalInOut(board.D27)
        self.emergency_stop.direction = digitalio.Direction.OUTPUT
        self.emergency_stop.value = False
        
        # Calibration parameters
        self.co2_zero = 0.0  # Zero offset in ppm
        self.co2_span = 1.0  # Span correction factor
        
        # Control parameters
        self.flow_setpoint = 1.5  # L/min
        
        # Data buffers for filtering
        self.co2_buffer = np.zeros(5)  # 5-point moving average
        self.buffer_index = 0
        
    def read_co2_analog(self):
        """Read CO2 via analog input with filtering"""
        # Read from AIN0 with 2:1 divider compensation
        voltage = self.adc.read_single_channel(0) * 2.0
        
        # Convert 0-5V to 0-1000 ppm with calibration
        ppm_raw = voltage * 200.0
        ppm_calibrated = (ppm_raw - self.co2_zero) * self.co2_span
        
        # Apply moving average filter
        self.co2_buffer[self.buffer_index] = ppm_calibrated
        self.buffer_index = (self.buffer_index + 1) % 5
        ppm_filtered = np.mean(self.co2_buffer)
        
        return ppm_raw, ppm_calibrated, ppm_filtered
    
    def read_pressure(self):
        """Read pressure sensor"""
        # Read from AIN1 with 2:1 divider
        voltage = self.adc.read_single_channel(1) * 2.0
        # Convert to kPa (0.2-4.7V = 0-100 kPa)
        if voltage < 0.2:
            voltage = 0.2
        kpa = (voltage - 0.2) * 100.0 / 4.5
        return kpa
    
    def read_dewpoint(self):
        """Read dew point sensor (4-20mA)"""
        # Read from AIN2 with current shunt and divider
        voltage = self.adc.read_single_channel(2) * 2.0
        # Convert 1-5V to dew point temperature
        # Assuming -60 to +60°C range
        if voltage < 1.0:
            voltage = 1.0
        dewpoint = (voltage - 1.0) * 30.0 - 60.0
        return dewpoint
    
    def read_temperature(self):
        """Read thermocouple temperature"""
        return self.thermocouple.temperature
    
    def read_flow(self):
        """Read flow meter"""
        try:
            flow, temp = self.flow_meter.read_measurement()
            return flow / 1000.0  # Convert to L/min
        except:
            return None
    
    def set_pump_pwm(self, duty_percent):
        """Set pump speed via PWM (0-100%)"""
        duty = int(duty_percent * 655.35)
        self.pwm.channels[0].duty_cycle = duty
    
    def set_heater_pwm(self, duty_percent):
        """Set heater power via PWM (0-100%)"""
        duty = int(duty_percent * 655.35)
        self.pwm.channels[1].duty_cycle = duty
    
    def calibrate_zero(self, zero_gas_ppm=0):
        """Calibrate zero point with zero gas"""
        print(f"Calibrating zero with {zero_gas_ppm} ppm gas...")
        time.sleep(30)  # Allow stabilization
        
        readings = []
        for _ in range(50):  # 5 seconds at 10 Hz
            voltage = self.adc.read_single_channel(0) * 2.0
            ppm_raw = voltage * 200.0
            readings.append(ppm_raw)
            time.sleep(0.1)
        
        avg_reading = np.mean(readings)
        self.co2_zero = avg_reading - zero_gas_ppm
        print(f"Zero calibration complete. Offset: {self.co2_zero:.1f} ppm")
    
    def calibrate_span(self, span_gas_ppm=400):
        """Calibrate span with span gas"""
        print(f"Calibrating span with {span_gas_ppm} ppm gas...")
        time.sleep(30)  # Allow stabilization
        
        readings = []
        for _ in range(50):
            voltage = self.adc.read_single_channel(0) * 2.0
            ppm_raw = voltage * 200.0
            ppm_zeroed = ppm_raw - self.co2_zero
            readings.append(ppm_zeroed)
            time.sleep(0.1)
        
        avg_reading = np.mean(readings)
        self.co2_span = span_gas_ppm / avg_reading
        print(f"Span calibration complete. Factor: {self.co2_span:.3f}")
    
    def emergency_shutdown(self):
        """Emergency stop all systems"""
        self.emergency_stop.value = True
        self.set_pump_pwm(0)
        self.set_heater_pwm(0)
        print("EMERGENCY SHUTDOWN ACTIVATED")
    
    def run_control_loop(self):
        """Main control loop at 10 Hz"""
        loop_time = 0.1  # 100ms = 10 Hz
        
        # CSV header
        print("timestamp,co2_raw,co2_cal,co2_filt,pressure,temp,dewpoint,flow")
        
        while True:
            start_time = time.time()
            
            # Read all sensors
            co2_raw, co2_cal, co2_filt = self.read_co2_analog()
            pressure = self.read_pressure()
            temperature = self.read_temperature()
            dewpoint = self.read_dewpoint()
            flow = self.read_flow()
            
            # Safety checks
            if pressure > 25:  # kPa
                self.emergency_shutdown()
                break
            
            if temperature < dewpoint + 3:
                # Activate heater to prevent condensation
                heater_duty = min(100, (dewpoint + 5 - temperature) * 20)
                self.set_heater_pwm(heater_duty)
            else:
                self.set_heater_pwm(0)
                
            # Flow control (simplified PID)
            if flow is not None:
                error = self.flow_setpoint - flow
                pump_duty = 50 + error * 20  # P control
                pump_duty = max(20, min(100, pump_duty))
                self.set_pump_pwm(pump_duty)
            
            # Log data in CSV format
            timestamp = time.time()
            print(f"{timestamp:.3f},{co2_raw:.1f},{co2_cal:.1f},{co2_filt:.1f},"
                  f"{pressure:.1f},{temperature:.1f},{dewpoint:.1f},"
                  f"{flow:.2f if flow else 'NA'}")
            
            # Maintain loop timing
            elapsed = time.time() - start_time
            if elapsed < loop_time:
                time.sleep(loop_time - elapsed)

if __name__ == "__main__":
    controller = ChamberController()
    
    # Optional calibration on startup
    # controller.calibrate_zero(0)  # With N2 or zero air
    # controller.calibrate_span(400)  # With span gas
    
    try:
        controller.run_control_loop()
    except KeyboardInterrupt:
        controller.emergency_shutdown()
        print("\nShutdown complete")
```

### H.6 Data Acquisition Performance

**10 Hz Sampling Analysis**:
- ADS1256 at 100 SPS with 8 channels = 12.5 Hz per channel
- Actual achievable: 10 Hz synchronized across all channels
- CO₂ resolution: ~0.1 ppm (noise limited)
- Timing jitter: <5 ms with proper code optimization

**Data Storage Options**:
1. Local CSV file with rotation
2. InfluxDB time-series database
3. MQTT publish to remote server
4. Direct PostgreSQL logging

### H.7 Advantages of Analog-Only Configuration

1. **Simplified wiring**: No RS485 termination or bias resistors
2. **True 10 Hz data**: No 2 Hz limitation from digital interface
3. **Better synchronization**: All sensors read in same ADC scan
4. **Lower latency**: Direct voltage measurement vs. serial protocol
5. **Cost reduction**: No RS485 HAT needed (~$30 saved)

**Trade-offs**:
- Accuracy: ±6.5 ppm + 2% (analog) vs ±5 ppm + 2% (digital)
- Calibration: Must implement in software
- Diagnostics: No access to internal sensor status

---

*Document Version: 2.0*  
*Date: July 2025*  
*Designer: Technical Engineering Division*loop()
    except KeyboardInterrupt:
        controller.emergency_shutdown()
        print("\nShutdown complete")
```

### H.6 Power Supply Design

```
┌─────────────────┐
│ 12V 5A Supply   │
│                 ├─── 12V → Pump (KNF NMP830)
│                 ├─── 12V → Heater (20W)
│                 ├─── 12V → Cooling Fan
└────────┬────────┘
         │
    Buck Converter
    (LM2596 Module)
         │
         ├─── 5V 3A → Raspberry Pi USB-C
         └─── 5V 1A → Sensors (GMP343, etc.)
```

### H.7 Enclosure and Thermal Management

**Recommended Setup**:
- DIN rail mounted components
- Forced air cooling for RPi
- Isolation between power and signal
- EMI shielding for ADC section

---

*Document Version: 1.9*  
*Date: July 2025*  
*Designer: Technical Engineering Division*