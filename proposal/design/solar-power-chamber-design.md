# Solar Power System Design for Chamber Control
## Off-Grid DC Power Solution with LiFePO4 Battery Storage

### Table of Contents
1. [System Overview](#system-overview)
2. [Solar Power Components](#solar-power-components)
3. [Power Budget Analysis](#power-budget-analysis)
4. [Charge Controller Configuration](#charge-controller-configuration)
5. [Battery System Design](#battery-system-design)
6. [DC Power Distribution](#dc-power-distribution)
7. [Modified Enclosure Design](#modified-enclosure-design)
8. [Protection and Monitoring](#protection-and-monitoring)
9. [Installation Guidelines](#installation-guidelines)
10. [Operating Considerations](#operating-considerations)

---

## 1. System Overview

This document specifies a complete solar-powered DC system for the chamber control, eliminating AC mains dependency for remote or field deployment applications.

### 1.1 System Architecture

```
Solar Panel (100W) → MPPT Controller → LiFePO4 Battery (100Ah)
                            ↓                    ↓
                     Direct Load Output    Battery Output
                            ↓                    ↓
                    Day Operations        24/7 Operations
                                               ↓
                                        DC Distribution
                                          ├── 12V Bus
                                          └── 5V Bus
```

### 1.2 Key Specifications
- **Autonomy**: 48-72 hours without sun
- **Daily Energy**: ~150 Wh consumption
- **Peak Power**: 45W (with heater active)
- **Average Power**: 15W continuous

---

## 2. Solar Power Components

### 2.1 Solar Panel Specifications

**Recommended Panel**: Renogy RNG-100D-SS
- Type: Monocrystalline
- Power: 100W (STC)
- Vmp: 17.9V
- Imp: 5.72A
- Voc: 21.6V
- Isc: 6.24A
- Dimensions: 1015 x 665 x 35mm
- Weight: 7.5 kg

### 2.2 Charge Controller

**Recommended Controller**: Victron SmartSolar MPPT 75/15
- Max PV voltage: 75V
- Max charge current: 15A
- Battery voltage: 12/24V auto
- Self-consumption: 10mA
- Efficiency: 98% max
- Bluetooth monitoring
- Programmable load output

### 2.3 Battery Specifications

**Recommended Battery**: Battle Born BB10012
- Chemistry: LiFePO4
- Capacity: 100Ah @ 12.8V
- Energy: 1280 Wh
- Max continuous discharge: 100A
- Max continuous charge: 50A
- Cycles: 3000-5000 @ 100% DoD
- BMS: Integrated with Bluetooth
- Operating temp: -20°C to 60°C

### 2.4 Supporting Components

| Component | Model | Purpose |
|-----------|-------|---------|
| PV Breaker | Midnite Solar MNEPV20 | 20A DC breaker |
| Battery Breaker | Blue Sea 7220 | 30A DC breaker |
| Fuse Block | Blue Sea 5025 | Distribution |
| DC Meter | Victron BMV-712 | Battery monitor |
| Surge Protector | Midnite Solar MNSPD | Lightning protection |

---

## 3. Power Budget Analysis

### 3.1 Load Analysis

| Component | Voltage | Current | Power | Duty Cycle | Daily Energy |
|-----------|---------|---------|-------|------------|--------------|
| RPi 4 | 5V | 1.5A | 7.5W | 100% | 180 Wh |
| ADC Board | 5V | 0.2A | 1W | 100% | 24 Wh |
| Sensors | 5V | 0.3A | 1.5W | 100% | 36 Wh |
| Pump | 12V | 0.45A | 5.5W | 50% | 66 Wh |
| Heater | 12V | 1.67A | 20W | 10% | 48 Wh |
| Fans | 12V | 0.2A | 2.4W | 50% | 28.8 Wh |
| **Total** | - | - | **37.9W** | - | **382.8 Wh** |

### 3.2 Solar Sizing Calculations

```
Required Daily Energy: 383 Wh
System Losses (85% eff): 450 Wh
Peak Sun Hours (worst): 3 hours
Required Panel Power: 450/3 = 150W

100W Panel Output:
Summer (6 PSH): 600 Wh/day
Winter (3 PSH): 300 Wh/day
Cloudy (1.5 PSH): 150 Wh/day
```

### 3.3 Battery Autonomy

```
Battery Capacity: 100Ah × 12.8V = 1280 Wh
Usable (80% DoD): 1024 Wh
Daily Consumption: 450 Wh

Autonomy = 1024/450 = 2.3 days
With reduced operation: >3 days
```

---

## 4. Charge Controller Configuration

### 4.1 Victron SmartSolar Settings

```
Battery Settings (LiFePO4):
- Absorption voltage: 14.2V
- Float voltage: 13.5V
- Equalization: Disabled
- Temperature comp: -16mV/°C
- Low temp cutoff: 5°C

Load Output Settings:
- Mode: User defined
- Turn on voltage: 13.0V
- Turn off voltage: 12.2V
- Used for: Non-critical loads
```

### 4.2 MPPT Operation Modes

```
Morning: Battery Charge Priority
├── 0-20W: All to battery
├── 20-40W: Battery + small loads
└── >40W: Full operation

Afternoon: Float + Load Support
├── Battery full
├── Direct solar to loads
└── Excess curtailed

Evening: Battery Discharge
├── No solar input
├── Battery supplies all loads
└── Load shedding if needed
```

---

## 5. Battery System Design

### 5.1 BMS Integration

```
Battle Born Internal BMS:
- Cell balancing: Automatic
- Over-voltage: 14.8V cutoff
- Under-voltage: 10.0V cutoff
- Over-current: 100A cutoff
- Temperature: -20°C to 60°C
- Bluetooth monitoring

External Monitoring:
BMV-712 → Shunt → Battery → Loads
         ↓
    SOC Display
```

### 5.2 Temperature Management

```
Battery Enclosure:
┌─────────────────────┐
│  Insulated Box      │
│  ┌───────────────┐  │
│  │   LiFePO4     │  │ ← 2" foam insulation
│  │   Battery     │  │
│  │  + Heater Pad │  │ ← 10W @ 12V
│  └───────────────┘  │
│  Thermostat: 5°C    │
└─────────────────────┘
```

---

## 6. DC Power Distribution

### 6.1 Power Architecture

```
Solar Panel
    ↓
MPPT Controller ──┬── 20A Breaker ── Battery
                  │                      ↓
                  └── Load Output    30A Breaker
                         ↓               ↓
                   Non-Critical     Main DC Bus
                      Loads              ↓
                                   Distribution
                                   ├── 12V (Direct)
                                   └── 5V (Buck)
```

### 6.2 DC-DC Converters

**12V Distribution**: Direct from battery
- Pump: Direct connection
- Heater: PWM controlled
- Fans: Direct connection

**5V Converter**: Traco Power TSR 2-2450
- Input: 6.5-36V DC
- Output: 5V @ 2A
- Efficiency: 94%
- No heatsink required
- EMI: EN 55032 Class A

**Backup 5V**: Pololu D24V22F5
- Input: 6-36V DC
- Output: 5V @ 2.2A
- For redundancy

### 6.3 Distribution Wiring

```
Fuse Block Layout:
┌────────────────────────┐
│ IN: 12V from Battery   │
├────┬────┬────┬────┬───┤
│ 5A │ 3A │ 5A │10A │2A │
├────┼────┼────┼────┼───┤
│Pump│ RPi│Heat│Res│Fan │
└────┴────┴────┴────┴───┘

Wire Sizing:
- Battery cables: 8 AWG
- Distribution: 14 AWG
- Device leads: 18 AWG
```

---

## 7. Modified Enclosure Design

### 7.1 Outdoor Enclosure

**Recommended Enclosure**: Altelix NF141206
- NEMA 4X Fiberglass
- Dimensions: 14" x 12" x 6"
- UV resistant
- Lockable
- Temp range: -40°C to 90°C

### 7.2 Layout for Solar System

```
Enclosure Internal Layout:
┌─────────────────────────────────┐
│ MPPT  │ Breakers │  DC Meter   │ Top
├───────┴───────────┴─────────────┤
│                                 │
│    Raspberry Pi Assembly        │ Middle
│    (Original control system)    │
│                                 │
├─────────────────────────────────┤
│  DC-DC │ Fuses │ Terminal Block│ Bottom
└─────────────────────────────────┘

External Connections:
- Top: Solar panel MC4 connectors
- Bottom: Battery Anderson connector
- Side: Sensor bulkhead connectors
```

### 7.3 Thermal Management (Solar)

```
Passive Cooling Design:
┌─────────────────────┐
│  Shade Hood /////// │ ← Reflects solar heat
├─────────────────────┤
│  ▲ ▲ ▲ Vents ▲ ▲ ▲ │ ← Chimney effect
│  ┌───────────────┐  │
│  │  Electronics  │  │
│  └───────────────┘  │
│  ▼ ▼ ▼ Vents ▼ ▼ ▼ │ ← Cool air intake
└─────────────────────┘

- Intake vents: IP66 breathers
- Natural convection flow
- White enclosure color
```

---

## 8. Protection and Monitoring

### 8.1 Multi-Level Protection

```
Protection Hierarchy:
Solar Panel → Surge Protector
                ↓
         MPPT Controller → Battery
         (Electronic)    (BMS Protected)
              ↓              ↓
         Load Breaker   Main Breaker
              ↓              ↓
         Fuse Block    Emergency Stop
              ↓              ↓
           Devices      Full Shutdown
```

### 8.2 Monitoring System

**Local Display**: Victron BMV-712
- Battery voltage
- Current in/out
- State of charge
- Time to go
- Historical data

**Remote Monitoring Options**:
1. **Cellular**: 4G modem + VPN
2. **LoRaWAN**: For remote sites
3. **Satellite**: Iridium modem
4. **Local WiFi**: When available

### 8.3 Automated Load Management

```python
class SolarPowerManager:
    def __init__(self):
        self.battery_voltage = 0
        self.battery_soc = 0
        self.solar_power = 0
        
    def load_priority_control(self):
        """Manage loads based on available power"""
        
        if self.battery_soc < 20:
            # Critical mode - essential only
            self.disable_heater()
            self.reduce_sampling_rate()
            self.dim_displays()
            
        elif self.battery_soc < 50:
            # Conservative mode
            self.limit_heater_duty(25)
            self.normal_sampling()
            
        else:
            # Normal operation
            self.enable_all_features()
            
    def solar_tracking(self):
        """Adjust consumption based on generation"""
        if self.solar_power > 50:  # Watts
            # Excess power - run optional tasks
            self.run_data_upload()
            self.run_self_diagnostics()
```

---

## 9. Installation Guidelines

### 9.1 Solar Panel Mounting

**Ground Mount** (Recommended):
- Tilt angle: Latitude + 15° (winter)
- Adjustable tilt ideal
- Ground stakes or concrete base
- 3m from enclosure max

**Pole Mount** (Alternative):
- Side-of-pole mount
- Wind rating: 140 mph
- Height: 2-3m typical
- Lightning rod above panel

### 9.2 Grounding System

```
Grounding Architecture:
Solar Frame ────┐
               ╧══ Earth Ground Rod
MPPT Ground ────┤    (8ft copper)
               │
DC Negative ────┘  Single Point Ground
                   
Enclosure ─────╧══ Second Ground Rod
(Separated by >6ft)
```

### 9.3 Cable Runs

**Solar to Controller**:
- Cable: 10 AWG PV wire
- Connectors: MC4
- Conduit: Liquidtight 3/4"
- Max length: 30ft (1% loss)

**Controller to Battery**:
- Cable: 8 AWG THHN
- Breaker at both ends
- Fused disconnect capable
- Max length: 6ft

---

## 10. Operating Considerations

### 10.1 Seasonal Adjustments

| Season | Panel Angle | Heater Use | Battery Temp | Strategy |
|--------|------------|------------|--------------|----------|
| Summer | Latitude -15° | Minimal | Monitor high | Cooling priority |
| Fall | Latitude | Moderate | Optimal | Standard |
| Winter | Latitude +15° | Maximum | Heat required | Conservation |
| Spring | Latitude | Low | Optimal | Growth mode |

### 10.2 Maintenance Schedule

**Daily** (Automated):
- Battery voltage check
- Solar production log
- Load consumption log
- Temperature monitoring

**Monthly**:
- Clean solar panel
- Check connections
- Download data logs
- Verify breaker operation

**Annually**:
- Torque all connections
- Test protection devices
- Update firmware
- Replace weatherstripping

### 10.3 Emergency Procedures

```
Power Conservation Modes:

Level 1 (SOC < 50%):
- Reduce sampling to 1 Hz
- Disable heater
- Minimal pump operation

Level 2 (SOC < 30%):
- Essential measurements only
- 0.1 Hz sampling
- Local storage only

Level 3 (SOC < 20%):
- Shutdown non-critical
- Preserve data
- Send distress beacon

Level 4 (SOC < 10%):
- Complete shutdown
- BMS protection active
```

### 10.4 System Performance

**Expected Uptime**:
- Full sun: 100%
- Partial clouds: 99%
- Heavy overcast: 95%
- Multi-day storms: 85%

**Data Integrity**:
- Local backup: SD card
- Power loss recovery: Automatic
- Measurement gaps: Logged
- Calibration stable: ±0.1%

---

## Appendix A: Component Suppliers

| Component | Supplier | Part Number | Price |
|-----------|----------|-------------|-------|
| Solar Panel | Renogy | RNG-100D-SS | $100 |
| MPPT Controller | Victron | SCC010015050R | $120 |
| LiFePO4 Battery | Battle Born | BB10012 | $950 |
| Battery Monitor | Victron | BMV-712 | $180 |
| DC Converter | Traco | TSR 2-2450 | $25 |
| Enclosure | Altelix | NF141206 | $140 |

**Total System Cost**: ~$1,600

## Appendix B: Wiring Diagram

```
         ┌─────────────┐
         │ Solar Panel │
         │   100W PV   │
         └──┬──────┬──┘
            │ MC4  │
    ┌───────┴──────┴───────┐
    │   MPPT Controller    │
    │  ┌─────────────────┐ │
    │  │ Victron 75/15   │ │
    │  └────┬──────┬─────┘ │
    └───────┼──────┼───────┘
            │ 8AWG │
    ┌───────┴──────┴───────┐
    │    LiFePO4 Battery   │
    │       100Ah 12V      │
    └───────┬──────┬───────┘
            │      │
         ┌──┴──┐ ┌─┴──┐
         │ 12V │ │ 5V │
         │ Bus │ │Conv│
         └──┬──┘ └─┬──┘
            │      │
         Loads  RPi System
```

---

*Document Version: 1.0*  
*Date: July 2025*  
*Solar System Design: Technical Engineering Division*