// Multi-site Worker script with comprehensive anti-spam protection
// Uses Cloudflare's free security features and custom rate limiting

import { EmailMessage } from 'cloudflare:email';
import { createMimeMessage } from 'mimetext';

// Security and validation constants
const SECURITY_CONSTANTS = {
  // Cloudflare security thresholds
  MIN_BOT_SCORE: 30,                    // Bot management score threshold (0-99, lower is more likely bot)
  MAX_THREAT_SCORE: 10,                 // Threat score threshold (0-100, higher is more threatening)
  
  // Email validation limits
  MAX_EMAIL_LENGTH: 254,                // RFC 5322 email address limit
  MAX_LOCAL_PART_LENGTH: 64,            // RFC 5322 local part limit
  
  // Rate limiting time constants
  RATE_LIMIT_WINDOW_SECONDS: 3600,      // 1 hour in seconds
  RATE_LIMIT_EMAIL_WINDOW_SECONDS: 24 * 60 * 60, // 24 hours in seconds
  
  // CORS cache duration
  CORS_MAX_AGE_SECONDS: 86400,          // 24 hours
};

// HTTP status codes
const HTTP_STATUS = {
  OK: 200,
  BAD_REQUEST: 400,
  NOT_FOUND: 404,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
};

// Standardized logging utility
const Logger = {
  // Info level for general application flow
  info: (message, data = null) => {
    const timestamp = new Date().toISOString();
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    console.log(`[${timestamp}] INFO: ${message}${logData}`);
  },

  // Warning level for potentially problematic situations
  warn: (message, data = null) => {
    const timestamp = new Date().toISOString();
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    console.log(`[${timestamp}] WARN: ${message}${logData}`);
  },

  // Error level for error conditions
  error: (message, error = null, data = null) => {
    const timestamp = new Date().toISOString();
    const errorInfo = error ? ` | Error: ${error.message || error}` : '';
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    console.error(`[${timestamp}] ERROR: ${message}${errorInfo}${logData}`);
  },

  // Security level for security-related events
  security: (action, reason, data = null) => {
    const timestamp = new Date().toISOString();
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    console.log(`[${timestamp}] SECURITY: ${action} - ${reason}${logData}`);
  },

  // Debug level for debugging information
  debug: (message, data = null) => {
    const timestamp = new Date().toISOString();
    const logData = data ? ` | Data: ${JSON.stringify(data)}` : '';
    console.log(`[${timestamp}] DEBUG: ${message}${logData}`);
  }
};

// Configuration for different sites
const SITE_CONFIGS = {
  'alieutica.com': {
    fromEmail: '<EMAIL>',
    toEmail: '<EMAIL>',
    siteName: 'Alieutica Contact Form'
  },
  'dolcevitaduo.com': {
    fromEmail: '<EMAIL>',
    toEmail: '<EMAIL>',
    siteName: 'Dolce Vita Duo Contact Form'
  },
  'opendojo.com': {
    fromEmail: '<EMAIL>',
    toEmail: '<EMAIL>',
    siteName: 'OpenDojo Contact Form'
  }
};

// Fallback configuration
const DEFAULT_CONFIG = {
  fromEmail: '<EMAIL>',
  toEmail: '<EMAIL>',
  siteName: 'Contact Form'
};

// Content validation constants  
const CONTENT_LIMITS = {
  MAX_MESSAGE_LENGTH: 5000,      // Maximum message length
  MIN_MESSAGE_LENGTH: 10,        // Minimum message length  
  MAX_SUBJECT_LENGTH: 200,       // Maximum subject length
  MAX_LINKS_ALLOWED: 2,          // Maximum links in message
  MAX_SPAM_WORDS_THRESHOLD: 2,   // Maximum suspicious words before flagging
};

// Spam detection configuration
const SPAM_CONFIG = {
  // Rate limiting
  maxSubmissionsPerIP: 5,        // Max submissions per IP per time window
  timeWindow: SECURITY_CONSTANTS.RATE_LIMIT_WINDOW_SECONDS,
  maxSubmissionsPerEmail: 3,     // Max submissions per email per day

  // Content filtering
  maxMessageLength: CONTENT_LIMITS.MAX_MESSAGE_LENGTH,
  minMessageLength: CONTENT_LIMITS.MIN_MESSAGE_LENGTH,
  maxSubjectLength: CONTENT_LIMITS.MAX_SUBJECT_LENGTH,

  // Suspicious patterns
  suspiciousWords: [
    'crypto', 'bitcoin', 'investment', 'loan', 'mortgage', 'casino',
    'viagra', 'pills', 'pharmacy', 'weight loss', 'make money',
    'click here', 'buy now', 'limited time', 'act now', 'urgent',
    'congratulations', 'winner', 'prize', 'lottery', 'inheritance'
  ],

  // Suspicious patterns in URLs/emails
  suspiciousPatterns: [
    /bit\.ly|tinyurl|short\.link/i,     // URL shorteners
    /\d{4,}/,                           // Long numbers (often spam)
    /[A-Z]{5,}/,                        // Excessive caps
    /(.)\1{4,}/,                        // Repeated characters
    /\$\d+|\d+\$|USD|EUR|GBP/i         // Currency mentions
  ]
};

// Pre-compiled regex patterns for performance
const EMAIL_VALIDATION_REGEX = {
  // Single comprehensive RFC 5322 compliant regex
  RFC_COMPLIANT: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,
  // Check for problematic patterns
  CONSECUTIVE_DOTS: /\.\./,
  DOT_AT_EDGES: /^\.|\.$|@\./
};

// Optimized email validation function
function validateEmail(email) {
  // Trim whitespace
  email = email.trim();

  // Fast length checks first (cheapest operations)
  if (email.length > SECURITY_CONSTANTS.MAX_EMAIL_LENGTH) {
    return { valid: false, reason: 'Email address too long' };
  }
  if (email.length < 5) { // Minimum valid email: a@b.c
    return { valid: false, reason: 'Email address too short' };
  }

  // Check for problematic patterns before expensive regex
  if (EMAIL_VALIDATION_REGEX.CONSECUTIVE_DOTS.test(email) || 
      EMAIL_VALIDATION_REGEX.DOT_AT_EDGES.test(email)) {
    return { valid: false, reason: 'Invalid dot placement in email' };
  }

  // Single comprehensive RFC 5322 validation (most expensive operation)
  if (!EMAIL_VALIDATION_REGEX.RFC_COMPLIANT.test(email)) {
    return { valid: false, reason: 'Invalid email format' };
  }

  // Local part length check (after we know email has valid format)
  const atIndex = email.indexOf('@');
  if (atIndex > SECURITY_CONSTANTS.MAX_LOCAL_PART_LENGTH) {
    return { valid: false, reason: 'Email local part too long' };
  }

  // Domain length check (minimum 2 chars after @)
  const domainLength = email.length - atIndex - 1;
  if (domainLength < 2) {
    return { valid: false, reason: 'Invalid domain format' };
  }

  return { valid: true, email: email };
}

// Optimized email template builders
const EmailTemplates = {
  // Pre-built HTML template parts (avoid string recreation)
  htmlHeader: (siteName) => `<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">
        Form Submission - ${siteName}
      </h2>`,
      
  htmlFormData: (name, email, subject, siteName) => `<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <p><strong>Name:</strong> ${name}</p>
        <p><strong>Email:</strong> <a href="mailto:${email}">${email}</a></p>
        <p><strong>Subject:</strong> ${subject}</p>
        <p><strong>From Site:</strong> ${siteName}</p>
      </div>`,
      
  htmlMessage: (message) => `<div style="background: white; padding: 20px; border-left: 4px solid #667eea; margin: 20px 0;">
        <h3 style="color: #333; margin-top: 0;">Message:</h3>
        <p style="line-height: 1.6; color: #555;">${message.replace(/\n/g, '<br>')}</p>
      </div>`,
      
  htmlSecurityInfo: (ip, country, userAgent, origin) => `<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <h4 style="color: #666; margin-top: 0;">Security Information:</h4>
        <p style="font-size: 12px; color: #666; margin: 5px 0;">
          <strong>IP Address:</strong> ${ip}<br>
          <strong>Country:</strong> ${country}<br>
          <strong>User Agent:</strong> ${userAgent}<br>
          <strong>Origin:</strong> ${origin}
        </p>
      </div>`,
      
  htmlFooter: (siteName) => `<hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
      <p style="color: #888; font-size: 12px;">
        This email was sent from ${siteName} contact form.
        <br>Sent at: ${new Date().toLocaleString()}
        <br>Passed all security checks ✓
      </p>
    </div>`,

  // Optimized builders
  buildHtml: (data) => {
    return EmailTemplates.htmlHeader(data.siteName) +
           EmailTemplates.htmlFormData(data.name, data.email, data.subject, data.siteName) +
           EmailTemplates.htmlMessage(data.message) +
           EmailTemplates.htmlSecurityInfo(data.ip, data.country, data.userAgent, data.origin) +
           EmailTemplates.htmlFooter(data.siteName);
  },

  buildText: (data) => {
    return `Form Submission - ${data.siteName}

Name: ${data.name}
Email: ${data.email}
Subject: ${data.subject}
From Site: ${data.siteName}

Message:
${data.message}

Security Information:
IP Address: ${data.ip}
Country: ${data.country}
User Agent: ${data.userAgent}
Origin: ${data.origin}

---
This email was sent from ${data.siteName} contact form.
Sent at: ${new Date().toLocaleString()}
Passed all security checks ✓`;
  }
};

// Safe header value function
function sanitizeHeaderValue(value) {
  if (!value) return '';

  // Remove any non-printable characters and normalize whitespace
  return value
    .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

// HTML entity encoding function to prevent XSS in email content
function escapeHtml(text) {
  if (!text) return '';
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

// Utility functions for spam detection
function getSiteConfig(request) {
  // Check explicit site config header
  const siteHeader = request.headers.get('X-Site-Config');
  Logger.debug('Site config header check', { siteHeader });
  if (siteHeader && SITE_CONFIGS[siteHeader]) {
    Logger.info('Using explicit site config from header', { site: siteHeader });
    return SITE_CONFIGS[siteHeader];
  }

  Logger.info('No matching site config found, using default');
  return DEFAULT_CONFIG;
}

function validateCloudflareHeaders(request) {
  // Check Cloudflare bot management score (0-99, lower is more likely bot)
  const botScore = request.cf?.botManagement?.score;
  if (botScore !== undefined && botScore < SECURITY_CONSTANTS.MIN_BOT_SCORE) {
    return { valid: false, reason: 'Bot detection triggered' };
  }

  // Check threat score (0-100, higher is more threatening)
  const threatScore = request.cf?.threatScore;
  if (threatScore !== undefined && threatScore > SECURITY_CONSTANTS.MAX_THREAT_SCORE) {
    return { valid: false, reason: 'High threat score detected' };
  }

  // Check if request is from Tor (optional - you might want to allow this)
  if (request.cf?.isTorExitNode) {
    return { valid: false, reason: 'Tor exit node detected' };
  }

  return { valid: true };
}

function validateContentForSpam(name, email, subject, message) {
  const errors = [];

  // Length validation
  if (message.length > SPAM_CONFIG.maxMessageLength) {
    errors.push('Message too long');
  }
  if (message.length < SPAM_CONFIG.minMessageLength) {
    errors.push('Message too short');
  }
  if (subject.length > SPAM_CONFIG.maxSubjectLength) {
    errors.push('Subject too long');
  }

  // Check for suspicious words
  const fullText = `${name} ${email} ${subject} ${message}`.toLowerCase();
  const foundSpamWords = SPAM_CONFIG.suspiciousWords.filter(word =>
    fullText.includes(word.toLowerCase())
  );
  if (foundSpamWords.length > CONTENT_LIMITS.MAX_SPAM_WORDS_THRESHOLD) {
    errors.push(`Suspicious content detected: ${foundSpamWords.join(', ')}`);
  }

  // Check for suspicious patterns
  const suspiciousPatterns = SPAM_CONFIG.suspiciousPatterns.filter(pattern =>
    pattern.test(fullText)
  );
  if (suspiciousPatterns.length > 1) {
    errors.push('Suspicious patterns detected');
  }

  // Check for excessive links
  const linkCount = (message.match(/https?:\/\/\S+/gi) || []).length;
  if (linkCount > CONTENT_LIMITS.MAX_LINKS_ALLOWED) {
    errors.push('Too many links detected');
  }

  // Check for repeated submissions (same message)
  const messageHash = btoa(message.toLowerCase().replace(/\s+/g, ' ').trim());

  return {
    valid: errors.length === 0,
    errors,
    messageHash
  };
}

// Email generation function
function generateEmailContent(siteConfig, name, email, subject, message, request) {
  const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
  const userAgent = request.headers.get('User-Agent') || 'unknown';
  const country = request.cf?.country || 'unknown';

  // Sanitize data for email content with HTML entity encoding
  const sanitizedName = escapeHtml(sanitizeHeaderValue(name));
  const sanitizedMessage = escapeHtml(message.replace(/[\x00-\x1F\x7F-\x9F]/g, '')); // Remove control characters and escape HTML
  const escapedEmail = escapeHtml(email);
  const escapedSubject = escapeHtml(sanitizeHeaderValue(subject));
  const escapedSiteName = escapeHtml(siteConfig.siteName);
  const escapedClientIP = escapeHtml(clientIP);
  const escapedCountry = escapeHtml(country);
  const escapedUserAgent = escapeHtml(userAgent);
  const escapedOrigin = escapeHtml(request.headers.get('Origin') || 'Unknown');

  // Use optimized template builders
  const templateData = {
    name: sanitizedName,
    email: email,
    subject: escapedSubject,
    message: sanitizedMessage,
    siteName: escapedSiteName,
    ip: escapedClientIP,
    country: escapedCountry,
    userAgent: escapedUserAgent,
    origin: escapedOrigin
  };

  const htmlContent = EmailTemplates.buildHtml(templateData);
  const textContent = EmailTemplates.buildText(templateData);

  return { htmlContent, textContent };
}

// Request validation function
async function validateContactRequest(request, env) {
  // 1. Validate Cloudflare security headers
  const cfValidation = validateCloudflareHeaders(request);
  if (!cfValidation.valid) {
    Logger.security('Request blocked', cfValidation.reason);
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'Request blocked by security filter' }),
        {
          status: HTTP_STATUS.TOO_MANY_REQUESTS,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  // Get site configuration based on request
  const siteConfig = getSiteConfig(request);
  Logger.debug('Site configuration determined', { siteConfig });

  // Parse the form data
  let formData;
  try {
    formData = await request.json();
  } catch (error) {
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'Invalid JSON data' }),
        {
          status: HTTP_STATUS.BAD_REQUEST,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  const { name, email, subject, message } = formData;
  Logger.info('Processing form submission', { 
    email, 
    name, 
    subject, 
    messageLength: message.length 
  });

  // 2. Basic validation
  if (!name || !email || !subject || !message) {
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'All fields are required' }),
        {
          status: HTTP_STATUS.BAD_REQUEST,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  // 3. Enhanced email validation
  const emailValidation = validateEmail(email);
  if (!emailValidation.valid) {
    Logger.security('Email validation failed', emailValidation.reason, { email });
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'Invalid email address' }),
        {
          status: HTTP_STATUS.BAD_REQUEST,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  const validatedEmail = emailValidation.email;

  // 4. Content spam validation
  const contentValidation = validateContentForSpam(name, validatedEmail, subject, message);
  if (!contentValidation.valid) {
    Logger.security('Spam content detected', contentValidation.errors.join(', '), { email: validatedEmail });
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'Message flagged as spam' }),
        {
          status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  // 5. Rate limiting check
  const rateLimitCheck = await checkRateLimit(request, validatedEmail, env);
  if (!rateLimitCheck.allowed) {
    Logger.security('Rate limit exceeded', rateLimitCheck.reason, { email: validatedEmail });
    return {
      valid: false,
      response: new Response(
        JSON.stringify({ error: 'Too many requests. Please try again later.' }),
        {
          status: HTTP_STATUS.TOO_MANY_REQUESTS,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
        }
      )
    };
  }

  return {
    valid: true,
    siteConfig,
    formData: { name, email: validatedEmail, subject, message }
  };
}

async function checkRateLimit(request, email, env) {
  // Skip rate limiting if KV is not available
  if (!env.MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV) {
    Logger.warn('KV namespace not available, skipping rate limiting');
    return { allowed: true };
  }

  const clientIP = request.headers.get('CF-Connecting-IP') || 'unknown';
  const now = Date.now();
  const hourAgo = now - (SPAM_CONFIG.timeWindow * 1000);
  const dayAgo = now - (SECURITY_CONSTANTS.RATE_LIMIT_EMAIL_WINDOW_SECONDS * 1000);

  try {
    // Check IP rate limit
    const ipKey = `ip:${clientIP}`;
    const ipSubmissions = await env.MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV.get(ipKey, 'json') || [];
    const recentIPSubmissions = ipSubmissions.filter(time => time > hourAgo);

    if (recentIPSubmissions.length >= SPAM_CONFIG.maxSubmissionsPerIP) {
      return {
        allowed: false,
        reason: 'Too many submissions from this IP address'
      };
    }

    // Check email rate limit
    const emailKey = `email:${email.toLowerCase()}`;
    const emailSubmissions = await env.MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV.get(emailKey, 'json') || [];
    const recentEmailSubmissions = emailSubmissions.filter(time => time > dayAgo);

    if (recentEmailSubmissions.length >= SPAM_CONFIG.maxSubmissionsPerEmail) {
      return {
        allowed: false,
        reason: 'Too many submissions from this email address'
      };
    }

    // Update rate limit counters
    const updatedIPSubmissions = [...recentIPSubmissions, now];
    const updatedEmailSubmissions = [...recentEmailSubmissions, now];

    await env.MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV.put(ipKey, JSON.stringify(updatedIPSubmissions), {
      expirationTtl: SPAM_CONFIG.timeWindow
    });
    await env.MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV.put(emailKey, JSON.stringify(updatedEmailSubmissions), {
      expirationTtl: SECURITY_CONSTANTS.RATE_LIMIT_EMAIL_WINDOW_SECONDS
    });

    return { allowed: true };
  } catch (error) {
    Logger.error('KV operation failed', error);
    // Allow submission if KV fails
    return { allowed: true };
  }
}

// Environment validation function
function validateEnvironment(env) {
  const requiredBindings = [
    { name: 'EMAIL_SENDER', type: 'email', required: true },
    { name: 'MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV', type: 'kv', required: false }
  ];

  const missing = [];
  const warnings = [];

  for (const binding of requiredBindings) {
    if (!env[binding.name]) {
      if (binding.required) {
        missing.push(`${binding.name} (${binding.type})`);
      } else {
        warnings.push(`${binding.name} (${binding.type}) - functionality will be limited`);
      }
    }
  }

  if (missing.length > 0) {
    const error = `Missing required environment bindings: ${missing.join(', ')}`;
    Logger.error('Environment validation failed', null, { missing, warnings });
    throw new Error(error);
  }

  if (warnings.length > 0) {
    Logger.warn('Optional environment bindings missing', { warnings });
  }

  Logger.info('Environment validation passed', { 
    bindings: requiredBindings.map(b => ({ name: b.name, available: !!env[b.name] }))
  });
}

export default {
  async fetch(request, env, ctx) {
    // Validate environment bindings on first request
    try {
      validateEnvironment(env);
    } catch (error) {
      Logger.error('Environment validation failed, worker cannot start', error);
      return new Response(
        JSON.stringify({ 
          error: 'Worker configuration error. Please check environment bindings.',
          details: error.message 
        }),
        {
          status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      Logger.debug('Handling CORS preflight request');
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, X-Site-Config',
          'Access-Control-Max-Age': SECURITY_CONSTANTS.CORS_MAX_AGE_SECONDS.toString(),
        },
      });
    }

    // Only handle POST requests to /api/contact
    if (request.method !== 'POST' || !request.url.includes('/api/contact')) {
      return new Response('Not Found', { status: HTTP_STATUS.NOT_FOUND });
    }

    try {
      // Validate the incoming request
      const validation = await validateContactRequest(request, env);
      if (!validation.valid) {
        return validation.response;
      }

      const { siteConfig, formData } = validation;
      const { name, email, subject, message } = formData;

      // Generate email content
      const { htmlContent, textContent } = generateEmailContent(
        siteConfig, name, email, subject, message, request
      );

      // Create and send email with proper header sanitization
      const msg = createMimeMessage();
      msg.setSender({ name: siteConfig.siteName, addr: siteConfig.fromEmail });
      msg.setRecipient(siteConfig.toEmail);

      // Sanitize subject for header
      const sanitizedSubject = sanitizeHeaderValue(subject);
      msg.setSubject(`${siteConfig.siteName} Contact: ${sanitizedSubject}`);

      msg.addMessage({
        contentType: 'text/plain',
        data: textContent,
      });

      msg.addMessage({
        contentType: 'text/html',
        data: htmlContent,
      });

      // Set Reply-To header with proper RFC 2822 format
      const sanitizedName = sanitizeHeaderValue(name);
      try {
        // Try with proper RFC format: "Name" <<EMAIL>>
        const replyToValue = `"${sanitizedName}" <${email}>`;
        msg.setHeader('Reply-To', replyToValue);
        Logger.debug('Reply-To header set successfully', { replyToValue });
      } catch (headerError) {
        Logger.debug('First Reply-To format failed, trying simple email format');
        try {
          // Fallback to just the email address
          msg.setHeader('Reply-To', email);
          Logger.debug('Reply-To header set with simple format', { email });
        } catch (secondError) {
          Logger.error('Failed to set Reply-To header with both formats', secondError);
          Logger.warn('Proceeding without Reply-To header');
          // Continue without Reply-To header if it fails
        }
      }

      const emailMessage = new EmailMessage(
        siteConfig.fromEmail,
        siteConfig.toEmail,
        msg.asRaw()
      );

      Logger.info('Attempting to send email', {
        from: siteConfig.fromEmail,
        to: siteConfig.toEmail,
        site: siteConfig.siteName
      });

      await env.EMAIL_SENDER.send(emailMessage);
      Logger.info('Email sent successfully', { site: siteConfig.siteName });

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Email sent successfully',
          site: siteConfig.siteName
        }),
        {
          status: HTTP_STATUS.OK,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );

    } catch (error) {
      Logger.error('Error sending email', error);

      // Show error details in development, generic message in production
      const isDevelopment = env.ENVIRONMENT === 'development' || env.NODE_ENV === 'development';
      
      return new Response(
        JSON.stringify({
          error: 'Failed to send email. Please try again later.',
          ...(isDevelopment && { details: error.message })
        }),
        {
          status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }
  },
};
