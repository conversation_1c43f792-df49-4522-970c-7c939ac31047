# Cloudflare Multi-Site Email Worker

## Development

Run the worker locally for development and testing:

```bash
# Start local development server
npx wrangler dev

# Check syntax
node -c src/index.js
```

This will:
- Run your worker locally (usually on http://localhost:8787)
- Use the preview KV namespace for rate limiting
- Show detailed error messages in API responses
- Auto-reload on file changes
- Display detailed logs in terminal

### Testing Locally

**Option 1: Direct API Testing with curl**
```bash
curl -X POST http://localhost:8787/api/contact \
  -H "Content-Type: application/json" \
  -H "X-Site-Config: alieutica.com" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "subject": "Test Subject",
    "message": "This is a test message from local development"
  }'
```

**Option 2: Browser Developer Console**
```javascript
fetch('http://localhost:8787/api/contact', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Test',
    email: '<EMAIL>',
    subject: 'Test',
    message: 'Test message'
  })
}).then(r => r.json()).then(console.log)
```

**Option 3: Update Frontend for Local Testing**
```javascript
// Temporarily change your frontend to use local URL
const API_URL = 'http://localhost:8787/api/contact';
```

⚠️ **Note about Local Email Testing**: When running locally with `wrangler dev`, emails are **NOT actually sent**. The Email API binding is mocked locally, so you'll get success responses but no actual emails will be delivered. This is normal Cloudflare behavior for local development. Real emails are only sent when deployed to production.

## Deployment

```bash
npm run deploy
# or
npx wrangler deploy
```

## Debugging

```bash
# View live logs from deployed worker
npx wrangler tail multi-site-contact-worker
```

## Configuration

Your Worker has access to the following bindings:
Binding                              Resource
env.EMAIL_SENDER (unrestricted)      Send Email
---

$ npx wrangler kv namespace create MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV

🌀 Creating namespace with title "MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV"
✨ Success!
Add the following to your configuration file in your kv_namespaces array:
[[kv_namespaces]]
binding = "MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV"
id = "daad720097a746fdbb682c67ccc65219"

$ npx wrangler kv namespace create MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV_PREVIEW

🌀 Creating namespace with title "MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV_PREVIEW"
✨ Success!
Add the following to your configuration file in your kv_namespaces array:
[[kv_namespaces]]
binding = "MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV_PREVIEW"
id = "d1aee176b3fb4f1a8363fda2c45efff6"
---

list existing KV namespaces:

```
npx wrangler kv namespace list
```

The preview_id ensures that when you run wrangler dev locally, it uses separate KV storage from production, which is good practice.
