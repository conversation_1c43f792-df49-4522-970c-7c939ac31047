# wrangler.toml
name = "multi-site-contact-worker"
main = "src/index.js"
compatibility_date = "2025-07-27"
compatibility_flags = ["nodejs_compat"]
send_metrics=false

# Email sending binding for Email Routing
[[send_email]]
name = "EMAIL_SENDER"

# KV namespace for rate limiting (optional but recommended)
[[kv_namespaces]]
binding = "MULTISITE_CONTACT_WORKER_RATE_LIMIT_KV"
id = "daad720097a746fdbb682c67ccc65219"
preview_id = "d1aee176b3fb4f1a8363fda2c45efff6"
