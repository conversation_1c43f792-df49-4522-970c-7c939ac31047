---
title: "Testing MDX Support"
date: "2025-01-12"
author: "<PERSON>"
excerpt: "Demonstrating the power of MDX in our Astro theme"
categories: ["Development", "Features"]
tags: ["mdx", "astro", "components"]
---

# Welcome to MDX!

This is a test post to demonstrate that MDX is working correctly in the Volks-Typo theme.

## What is MDX?

MDX lets you use JSX in your markdown content. This means you can:

1. Import and use components
2. Write JSX directly in your content
3. Create interactive documentation

## Example: Using JSX

Here's a simple example of JSX in MDX:

<div style={{ padding: '20px', backgroundColor: '#f5f5f5', borderRadius: '8px', marginBottom: '20px' }}>
  <h3 style={{ color: '#dc2626', marginBottom: '10px' }}>This is a JSX component!</h3>
  <p>You can use inline styles and create custom layouts right in your content.</p>
</div>

## Code Examples

You can still use regular markdown code blocks:

```javascript
function greet(name) {
  return `Hello, ${name}!`;
}
```

## Interactive Elements

<details>
  <summary style={{ cursor: 'pointer', fontWeight: 'bold', marginBottom: '10px' }}>Click to expand!</summary>
  <p>This is a native HTML details element that provides interactivity without JavaScript.</p>
</details>

## Conclusion

MDX support opens up a world of possibilities for creating rich, interactive content in your blog posts!