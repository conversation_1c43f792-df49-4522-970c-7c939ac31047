/* Volks-Typo Global Styles */

/* Color Variables */
:root {
  /* Monotone Palette */
  --color-white: #ffffff;
  --color-light-gray: #f5f5f5;
  --color-medium-gray: #888888;
  --color-dark-gray: #333333;
  --color-black: #000000;
  
  /* Accent Color */
  --color-accent-red: #dc2626;
  
  /* Semantic Colors */
  --color-background: var(--color-white);
  --color-surface: var(--color-light-gray);
  --color-text-primary: var(--color-black);
  --color-text-secondary: var(--color-dark-gray);
  --color-text-muted: var(--color-medium-gray);
  --color-border: var(--color-medium-gray);
  --color-accent: var(--color-accent-red);
  
  /* Typography */
  --font-heading-primary: 'Oswald', sans-serif;
  --font-heading-secondary: 'Roboto Condensed', sans-serif;
  --font-body: 'Work Sans', sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
  
  /* Layout */
  --grid-unit: 8px;
  --max-width: 1200px;
  --sidebar-width: 320px;
  --content-padding: calc(var(--grid-unit) * 3);
}

/* Dark Mode Theme */
[data-theme="dark"] {
  /* Monotone Palette - Dark Mode */
  --color-background: #0a0a0a;
  --color-surface: #1a1a1a;
  --color-text-primary: #f5f5f5;
  --color-text-secondary: #b8b8b8;
  --color-text-muted: #888888;
  --color-border: #333333;
  --color-accent: #ff3333; /* Slightly brighter red for dark mode */
}

/* Smooth theme transitions */
html {
  transition: background-color 0.3s ease;
}

body,
a,
h1, h2, h3, h4, h5, h6,
p, span, div,
button, input {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-body);
  font-size: 16px;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-background);
}

body {
  min-height: 100vh;
  font-size: 1rem;
  font-weight: 400;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.12em;
  color: var(--color-accent);
  margin-bottom: calc(var(--grid-unit) * 2);
}

h1 {
  font-family: var(--font-heading-primary);
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: 0.15em;
  margin-bottom: calc(var(--grid-unit) * 4);
}

h2 {
  font-family: var(--font-heading-secondary);
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: 0.1em;
}

h3 {
  font-family: var(--font-heading-secondary);
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: 0.08em;
}

p {
  margin-bottom: calc(var(--grid-unit) * 2);
  font-size: 1.125rem;
}

/* Links */
a {
  color: var(--color-accent);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover,
a:focus {
  color: var(--color-text-primary);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin-bottom: calc(var(--grid-unit) * 2);
  padding-left: calc(var(--grid-unit) * 3);
}

li {
  margin-bottom: calc(var(--grid-unit) / 2);
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--color-border);
  padding: calc(var(--grid-unit) * 2) calc(var(--grid-unit) * 3);
  margin: calc(var(--grid-unit) * 3) 0;
  background-color: var(--color-surface);
  font-style: italic;
}

/* Code */
code {
  font-family: var(--font-mono);
  font-size: 0.9em;
  background-color: var(--color-surface);
  padding: 2px 4px;
  border-radius: 3px;
  border: 1px solid var(--color-border);
}

pre {
  font-family: var(--font-mono);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  padding: calc(var(--grid-unit) * 2);
  margin: calc(var(--grid-unit) * 3) 0;
  overflow-x: auto;
  border-radius: 4px;
}

pre code {
  background: none;
  border: none;
  padding: 0;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  border: 1px solid var(--color-border);
  margin: calc(var(--grid-unit) * 2) 0;
}

figcaption {
  font-style: italic;
  font-size: 0.9rem;
  color: var(--color-text-muted);
  text-align: center;
  margin-top: calc(var(--grid-unit) / 2);
}

/* Horizontal Rule */
hr {
  border: none;
  height: 4px;
  background-color: var(--color-accent);
  margin: calc(var(--grid-unit) * 4) 0;
}


/* Utility Classes */
.text-center {
  text-align: center;
}

.text-muted {
  color: var(--color-text-muted);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}