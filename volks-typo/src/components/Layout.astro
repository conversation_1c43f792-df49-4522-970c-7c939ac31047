---
import '@fontsource/oswald/300.css';
import '@fontsource/oswald/400.css';
import '@fontsource/oswald/500.css';
import '@fontsource/oswald/600.css';
import '@fontsource/oswald/700.css';
import '@fontsource/roboto-condensed/300.css';
import '@fontsource/roboto-condensed/400.css';
import '@fontsource/roboto-condensed/700.css';
import '@fontsource/work-sans/400.css';
import '@fontsource/work-sans/500.css';
import '@fontsource/work-sans/600.css';
import '@fontsource/jetbrains-mono/400.css';
import '@fontsource/jetbrains-mono/700.css';
import '../styles/global.css';

import Header from './Header.astro';
import Sidebar from './Sidebar.astro';
import Footer from './Footer.astro';
import SEO from './SEO.astro';

export interface Props {
  title: string;
  description?: string;
  showSidebar?: boolean;
  image?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  canonical?: string;
}

const { title, description, showSidebar = true, image, type, publishedTime, canonical } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <script is:inline src="/theme-init.js"></script>
    <SEO
      title={title}
      description={description}
      image={image}
      type={type}
      publishedTime={publishedTime}
      canonical={canonical}
    />
  </head>
  <body>
    <div class="site-wrapper">
      <Header />
      
      <main class="main-content" class:list={{ 'with-sidebar': showSidebar }}>
        {showSidebar && (
          <aside class="sidebar">
            <Sidebar />
          </aside>
        )}
        
        <div class="content">
          <slot />
        </div>
      </main>
      
      <Footer />
    </div>
  </body>
</html>

<style>
  .site-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .main-content {
    flex: 1;
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
    width: 100%;
  }

  .main-content.with-sidebar {
    display: grid;
    grid-template-columns: 1fr;
    gap: calc(var(--grid-unit) * 4);
  }

  .content {
    min-width: 0;
    padding: calc(var(--grid-unit) * 3) 0;
  }

  .sidebar {
    order: -1;
    padding-top: calc(var(--grid-unit) * 3);
  }

  @media (min-width: 1024px) {
    .main-content.with-sidebar {
      grid-template-columns: var(--sidebar-width) 1fr;
    }
    
    .sidebar {
      order: 0;
      padding-top: calc(var(--grid-unit) * 3);
    }
  }
</style>