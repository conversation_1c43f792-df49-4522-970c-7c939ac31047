---
export interface Props {
  title?: string;
  description?: string;
  image?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  canonical?: string;
}

import { SITE_TITLE, SITE_DESCRIPTION } from '../config';

const {
  title = SITE_TITLE,
  description = SITE_DESCRIPTION,
  image = '/og-image.png',
  type = 'website',
  publishedTime,
  canonical,
} = Astro.props;

const pageTitle = title === SITE_TITLE ? title : `${title} | ${SITE_TITLE}`;
const canonicalURL = canonical || new URL(Astro.url.pathname, Astro.site);
const imageURL = new URL(image, Astro.site);
---

<title>{pageTitle}</title>
<meta name="description" content={description} />
<meta name="author" content="Your Name" />

<link rel="canonical" href={canonicalURL} />

<meta property="og:title" content={pageTitle} />
<meta property="og:description" content={description} />
<meta property="og:type" content={type} />
<meta property="og:url" content={canonicalURL} />
<meta property="og:image" content={imageURL} />
<meta property="og:site_name" content={SITE_TITLE} />
{publishedTime && <meta property="article:published_time" content={publishedTime} />}

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={pageTitle} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={imageURL} />

<meta name="robots" content="index, follow" />
<meta name="googlebot" content="index, follow" />

<link rel="alternate" type="application/rss+xml" title={SITE_TITLE} href="/rss.xml" />
<link rel="sitemap" type="application/xml" href="/sitemap-index.xml" />