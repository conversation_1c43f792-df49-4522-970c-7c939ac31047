---
import { config } from '../config';
import ThemeToggle from './ThemeToggle.astro';

const { base = '' } = Astro.site ? { base: import.meta.env.BASE_URL } : { base: '' };
---

<header class="site-header">
  <div class="header-content">
    <!-- Mobile hamburger button - far left on mobile -->
    <button class="hamburger-toggle mobile-only" id="hamburger-toggle" aria-label="Toggle navigation menu" aria-expanded="false">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path class="hamburger-line" d="M3 6h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path class="hamburger-line" d="M3 12h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path class="hamburger-line" d="M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
    </button>
    
    <div class="site-title">
      <a href={base}>
        <img src={base + "site-title.svg"} alt={config.title} class="title-svg" />
      </a>
    </div>
    
    <div class="header-controls">
      <nav class="main-navigation" role="navigation" aria-label="Main navigation">
        <ul class="nav-list">
          <li><a href={base} class="nav-link">Home</a></li>
          <li><a href={base + "blog"} class="nav-link">Blog</a></li>
          <li><a href={base + "categories"} class="nav-link">Categories</a></li>
          <li><a href={base + "about"} class="nav-link">About</a></li>
        </ul>
      </nav>
      
      <!-- Mobile controls group - RSS and Search on right -->
      <div class="mobile-controls">
        <ThemeToggle />
        <a href="https://github.com/jdrhyne2/volks-typo" class="github-star-button" target="_blank" rel="noopener" title="Star on GitHub">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <path d="M12 2C6.477 2 2 6.463 2 11.97c0 4.404 2.865 8.14 6.839 9.458.5.092.682-.216.682-.48 0-.236-.008-.864-.013-1.695-2.782.602-3.369-1.337-3.369-1.337-.454-1.151-1.11-1.458-1.11-1.458-.908-.618.069-.606.069-.606 1.003.07 1.531 1.027 1.531 1.027.892 1.524 2.341 1.084 2.91.828.092-.643.35-1.083.636-1.332-2.22-.251-4.555-1.107-4.555-4.927 0-1.088.39-1.979 1.029-2.675-.103-.252-.446-1.266.098-2.638 0 0 .84-.268 2.75 1.022A9.607 9.607 0 0112 6.82a9.55 9.55 0 012.504.336c1.909-1.29 2.747-1.022 2.747-1.022.546 1.372.202 2.386.1 2.638.64.696 1.028 1.587 1.028 2.675 0 3.83-2.339 4.673-4.566 4.92.359.307.678.915.678 1.846 0 1.332-.012 2.407-.012 2.734 0 .267.18.577.688.48 3.97-1.32 6.833-5.054 6.833-9.458C22 6.463 17.522 2 12 2z" fill="currentColor"/>
          </svg>
          <span class="github-star-text">Star</span>
        </a>
        
        <a href={base + "rss.xml"} class="rss-toggle" title="RSS Feed">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
            <circle cx="5" cy="19" r="2" fill="currentColor"/>
            <path d="M3 3v2c8.284 0 15 6.716 15 15h2c0-9.389-7.611-17-17-17z" fill="currentColor"/>
            <path d="M3 9v2c4.963 0 9 4.037 9 9h2c0-6.065-4.935-11-11-11z" fill="currentColor"/>
          </svg>
          <span class="visually-hidden">RSS</span>
        </a>
        
        <button class="search-toggle" id="search-toggle" aria-label="Search articles">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2" fill="none"/>
            <path d="M21 21l-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</header>

<nav class="mobile-menu" id="mobile-menu" aria-label="Mobile navigation">
  <ul class="mobile-nav-list">
    <li><a href={base} class="mobile-nav-link">Home</a></li>
    <li><a href={base + "blog"} class="mobile-nav-link">Blog</a></li>
    <li><a href={base + "categories"} class="mobile-nav-link">Categories</a></li>
    <li><a href={base + "about"} class="mobile-nav-link">About</a></li>
  </ul>
</nav>

<div class="search-overlay" id="search-overlay">
  <div class="search-container">
    <div class="search-header">
      <input type="text" class="search-input" id="search-input" placeholder="Search articles..." autocomplete="off">
      <button class="search-close" id="search-close" aria-label="Close search">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
          <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
        </svg>
      </button>
    </div>
    <div class="search-results" id="search-results"></div>
  </div>
</div>

<script>
  const base = `${import.meta.env.BASE_URL || ''}`;
  
  // Hamburger menu functionality
  function initHamburgerMenu() {
    const hamburgerToggle = document.getElementById('hamburger-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const body = document.body;

    if (!hamburgerToggle || !mobileMenu) return;

    hamburgerToggle.addEventListener('click', () => {
      const isOpen = mobileMenu.classList.contains('active');
      
      if (isOpen) {
        mobileMenu.classList.remove('active');
        hamburgerToggle.setAttribute('aria-expanded', 'false');
        body.style.overflow = '';
      } else {
        mobileMenu.classList.add('active');
        hamburgerToggle.setAttribute('aria-expanded', 'true');
        body.style.overflow = 'hidden';
      }
    });

    // Close menu when clicking on a link
    const mobileNavLinks = mobileMenu.querySelectorAll('.mobile-nav-link');
    mobileNavLinks.forEach(link => {
      link.addEventListener('click', () => {
        mobileMenu.classList.remove('active');
        hamburgerToggle.setAttribute('aria-expanded', 'false');
        body.style.overflow = '';
      });
    });

    // Close menu on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
        mobileMenu.classList.remove('active');
        hamburgerToggle.setAttribute('aria-expanded', 'false');
        body.style.overflow = '';
      }
    });
  }

  // Search functionality
  let searchData: any[] = [];
  let searchOverlay: HTMLElement | null, searchInput: HTMLInputElement | null, searchResults: HTMLElement | null;

  async function initSearch() {
    try {
      const response = await fetch(base + 'search.json');
      searchData = await response.json();
    } catch (error) {
      console.error('Failed to load search data:', error);
    }

    searchOverlay = document.getElementById('search-overlay');
    searchInput = document.getElementById('search-input') as HTMLInputElement;
    searchResults = document.getElementById('search-results');

    // Event listeners
    document.getElementById('search-toggle')?.addEventListener('click', openSearch);
    document.getElementById('search-close')?.addEventListener('click', closeSearch);
    searchInput?.addEventListener('input', handleSearch);
    
    // Close on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && searchOverlay?.classList.contains('active')) {
        closeSearch();
      }
    });
    
    // Close on overlay click
    searchOverlay?.addEventListener('click', (e: Event) => {
      if (e.target === searchOverlay) {
        closeSearch();
      }
    });
  }

  function openSearch() {
    searchOverlay?.classList.add('active');
    document.body.style.overflow = 'hidden';
    setTimeout(() => searchInput?.focus(), 100);
  }

  function closeSearch() {
    searchOverlay?.classList.remove('active');
    document.body.style.overflow = '';
    if (searchInput) searchInput.value = '';
    if (searchResults) searchResults.innerHTML = '';
  }

  function handleSearch(e: Event) {
    const target = e.target as HTMLInputElement;
    const query = target.value.toLowerCase().trim();
    
    if (query.length < 2) {
      if (searchResults) searchResults.innerHTML = '';
      return;
    }

    const results = searchData.filter((post: any) => 
      post.title.toLowerCase().includes(query) ||
      post.excerpt.toLowerCase().includes(query) ||
      (post.categories && post.categories.some((cat: string) => cat.toLowerCase().includes(query)))
    ).slice(0, 10);

    displayResults(results, query);
  }

  function displayResults(results: any[], query: string) {
    if (results.length === 0) {
      if (searchResults) {
        searchResults.innerHTML = `
          <div class="search-no-results">
            <p>No articles found for "${query}"</p>
          </div>
        `;
      }
      return;
    }

    const resultsHTML = results.map((post: any) => `
      <article class="search-result">
        <h3 class="search-result-title">
          <a href="${base}${post.url.startsWith('/') ? post.url.slice(1) : post.url}">${post.title}</a>
        </h3>
        <time class="search-result-date">${new Date(post.date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}</time>
        ${post.excerpt ? `<p class="search-result-excerpt">${post.excerpt}</p>` : ''}
        ${post.categories ? `
          <div class="search-result-categories">
            ${post.categories.map((cat: string) => `<span class="search-category">${cat}</span>`).join(' ')}
          </div>
        ` : ''}
      </article>
    `).join('');

    if (searchResults) {
      searchResults.innerHTML = `
        <div class="search-results-header">
          <p>Found ${results.length} article${results.length === 1 ? '' : 's'} for "${query}"</p>
        </div>
        ${resultsHTML}
      `;
    }
  }

  // Initialize when DOM is loaded
  function init() {
    initHamburgerMenu();
    initSearch();
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
</script>

<style>
  .site-header {
    background-color: var(--color-surface);
    border-bottom: 2px solid var(--color-border);
    padding: calc(var(--grid-unit) * 3) 0;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .header-content {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 var(--content-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) * 2);
  }

  .site-title a {
    display: block;
    line-height: 0;
  }

  .title-svg {
    height: 48px;
    width: auto;
    max-width: 280px;
    border: none !important; /* Override global img styles */
    margin: 0 !important; /* Override global img styles */
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: calc(var(--grid-unit) * 3);
    flex-shrink: 0;
  }

  .search-toggle {
    background: none;
    border: none;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit) / 2);
    cursor: pointer;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .search-toggle:hover,
  .search-toggle:focus {
    color: var(--color-accent);
    outline: none;
  }

  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .search-overlay.active {
    opacity: 1;
    visibility: visible;
  }

  .search-container {
    max-width: 800px;
    margin: calc(var(--grid-unit) * 10) auto calc(var(--grid-unit) * 4) auto;
    padding: 0 var(--content-padding);
  }

  .search-header {
    display: flex;
    align-items: center;
    background: var(--color-background);
    border: 2px solid var(--color-border);
    border-radius: 8px;
    padding: calc(var(--grid-unit) * 3);
    margin-bottom: calc(var(--grid-unit) * 5);
  }

  .search-input {
    flex: 1;
    border: none;
    background: none;
    font-family: var(--font-body);
    font-size: 1.125rem;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit) * 2) calc(var(--grid-unit) * 3);
    outline: none;
  }

  .search-input::placeholder {
    color: var(--color-text-muted);
  }

  .search-close {
    background: none;
    border: none;
    color: var(--color-text-muted);
    padding: calc(var(--grid-unit) / 2);
    cursor: pointer;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .search-close:hover,
  .search-close:focus {
    color: var(--color-accent);
    outline: none;
  }

  .search-results {
    max-height: 70vh;
    min-height: 40vh;
    overflow-y: auto;
    background: var(--color-background);
    border-radius: 8px;
    border: 1px solid var(--color-border);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: calc(var(--grid-unit) * 3) calc(var(--grid-unit) * 4) calc(var(--grid-unit) * 4) calc(var(--grid-unit) * 4);
  }

  .search-results:empty {
    display: none;
  }

  .search-results-header {
    padding: calc(var(--grid-unit) * 3) calc(var(--grid-unit) * 4);
    margin: calc(var(--grid-unit) * -3) calc(var(--grid-unit) * -4) calc(var(--grid-unit) * 3) calc(var(--grid-unit) * -4);
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface);
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .search-results-header p {
    margin: 0;
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .search-result {
    padding: calc(var(--grid-unit) * 6) calc(var(--grid-unit) * 6);
    margin-bottom: calc(var(--grid-unit) * 2);
    border-bottom: 1px solid var(--color-border);
    transition: background-color 0.2s ease;
  }

  .search-result:hover {
    background-color: var(--color-surface);
  }

  .search-result:last-child {
    border-bottom: none;
  }

  .search-result-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: calc(var(--grid-unit) * 3);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .search-result-title a {
    color: var(--color-text-primary);
    text-decoration: none;
  }

  .search-result-title a:hover,
  .search-result-title a:focus {
    color: var(--color-accent);
  }

  .search-result-date {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: block;
    margin-bottom: calc(var(--grid-unit) * 4);
  }

  .search-result-excerpt {
    color: var(--color-text-secondary);
    line-height: 1.8;
    margin-bottom: calc(var(--grid-unit) * 5);
  }

  .search-result-categories {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) * 2.5);
    margin-top: calc(var(--grid-unit) * 3);
  }

  .search-category {
    background-color: #e8a100;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit) * 1.5) calc(var(--grid-unit) * 2.5);
    font-size: 0.8rem;
    font-family: var(--font-mono);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .search-category:hover {
    background-color: #c13127;
    color: white;
  }

  /* Dark mode search category styles */
  :global([data-theme="dark"]) .search-category {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }

  :global([data-theme="dark"]) .search-category:hover {
    background-color: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
  }

  .search-no-results {
    padding: calc(var(--grid-unit) * 10) calc(var(--grid-unit) * 6);
    text-align: center;
  }

  .search-no-results p {
    color: var(--color-text-muted);
    font-style: italic;
    margin: 0;
    font-size: 1.1rem;
  }

  .main-navigation {
    flex-shrink: 0;
  }

  .nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: calc(var(--grid-unit) * 3);
  }

  .nav-link {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit) / 2) calc(var(--grid-unit));
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
  }

  .nav-link:hover,
  .nav-link:focus {
    color: var(--color-accent);
    border-bottom-color: var(--color-accent);
    text-decoration: none;
  }

  .rss-link {
    display: flex;
    align-items: center;
    gap: calc(var(--grid-unit) / 2);
  }

  .visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Desktop controls - visible by default */
  .mobile-controls {
    display: flex;
    align-items: center;
    gap: calc(var(--grid-unit) * 2);
  }

  /* Hide mobile-only hamburger by default */
  .mobile-only {
    display: none !important;
  }

  .hamburger-toggle {
    background: none;
    border: none;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit));
    cursor: pointer;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .rss-toggle {
    background: none;
    border: none;
    color: var(--color-text-primary);
    padding: calc(var(--grid-unit));
    cursor: pointer;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
  }
  
  .github-star-button {
    background: none;
    border: 2px solid var(--color-accent);
    color: var(--color-accent);
    padding: calc(var(--grid-unit) * 0.75) calc(var(--grid-unit) * 2);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: calc(var(--grid-unit));
    text-decoration: none;
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
    border-radius: 4px;
  }
  
  .github-star-button:hover,
  .github-star-button:focus {
    background-color: var(--color-accent);
    color: var(--color-background);
    outline: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .github-star-text {
    display: inline-block;
  }

  .hamburger-toggle:hover,
  .hamburger-toggle:focus,
  .rss-toggle:hover,
  .rss-toggle:focus {
    color: var(--color-accent);
    outline: none;
  }

  /* Mobile menu - hidden by default */
  .mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-background);
    z-index: 99;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
    padding-top: calc(var(--grid-unit) * 12);
  }

  .mobile-menu.active {
    transform: translateX(0);
  }

  .mobile-nav-list {
    list-style: none;
    margin: 0;
    padding: calc(var(--grid-unit) * 4) 0;
  }

  .mobile-nav-link {
    display: block;
    padding: calc(var(--grid-unit) * 3) calc(var(--grid-unit) * 4);
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    font-size: 1.25rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--color-text-primary);
    text-decoration: none;
    border-bottom: 1px solid var(--color-border);
    transition: all 0.2s ease;
  }

  .mobile-nav-link:hover,
  .mobile-nav-link:focus {
    color: var(--color-accent);
    background-color: var(--color-surface);
  }

  @media (max-width: 768px) {
    .site-header {
      padding: calc(var(--grid-unit) * 2) 0;
    }

    .header-content {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      align-items: center;
      gap: calc(var(--grid-unit) * 2);
      padding: 0 calc(var(--grid-unit) * 2);
    }

    /* Show mobile hamburger on far left */
    .mobile-only {
      display: flex !important;
      justify-self: start;
    }

    .site-title {
      justify-self: center;
      text-align: center;
      grid-column: 2; /* Explicitly place in center column */
    }

    .site-title a {
      display: inline-block;
    }

    .title-svg {
      height: 36px;
      width: auto;
      max-width: 180px;
    }
    
    .header-controls {
      justify-self: end;
      gap: 0;
      grid-column: 3; /* Explicitly place in right column */
    }
    
    /* Hide desktop navigation on mobile */
    .main-navigation {
      display: none;
    }
    
    /* Show mobile controls on right */
    .mobile-controls {
      display: flex !important;
      gap: calc(var(--grid-unit) * 1.5);
    }
    
    .hamburger-toggle,
    .rss-toggle,
    .search-toggle {
      padding: calc(var(--grid-unit) * 0.75);
    }

    .search-container {
      margin: calc(var(--grid-unit) * 5) auto calc(var(--grid-unit) * 3) auto;
      padding: 0 calc(var(--grid-unit) * 2);
    }

    .search-result {
      padding: calc(var(--grid-unit) * 4) calc(var(--grid-unit) * 3);
      margin-bottom: calc(var(--grid-unit) * 1.5);
    }

    .search-results {
      padding: calc(var(--grid-unit) * 2) calc(var(--grid-unit) * 2.5) calc(var(--grid-unit) * 3) calc(var(--grid-unit) * 2.5);
    }
    
    .search-results-header {
      padding: calc(var(--grid-unit) * 2) calc(var(--grid-unit) * 2.5);
      margin: calc(var(--grid-unit) * -2) calc(var(--grid-unit) * -2.5) calc(var(--grid-unit) * 2) calc(var(--grid-unit) * -2.5);
    }
    
    .search-result-categories {
      gap: calc(var(--grid-unit) * 2);
    }
    
    .search-category {
      padding: calc(var(--grid-unit)) calc(var(--grid-unit) * 2);
    }
    
    .search-result-title {
      margin-bottom: calc(var(--grid-unit) * 2);
    }
    
    .search-result-date {
      margin-bottom: calc(var(--grid-unit) * 3);
    }
    
    .search-result-excerpt {
      margin-bottom: calc(var(--grid-unit) * 4);
    }
    
    /* Adjust GitHub button for mobile */
    .github-star-button {
      padding: calc(var(--grid-unit) * 0.75);
      border-width: 1px;
    }
    
    .github-star-text {
      display: none;
    }
  }
  
  @media (max-width: 480px) {
    /* Even smaller mobile adjustments */
    .mobile-controls {
      gap: calc(var(--grid-unit)) !important;
    }
    
    .github-star-button,
    .rss-toggle,
    .search-toggle {
      padding: calc(var(--grid-unit) * 0.5);
    }
  }
</style>