---
import Layout from '../components/Layout.astro';
const base = import.meta.env.BASE_URL;

const mdPostsGlob = import.meta.glob('../content/blog/*.md', { eager: true });
const mdxPostsGlob = import.meta.glob('../content/blog/*.mdx', { eager: true });

const allPosts = [...Object.values(mdPostsGlob), ...Object.values(mdxPostsGlob)] as any[];
const sortedPosts = allPosts
  .sort((a: any, b: any) => new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime());
---

<Layout title="Blog | Volks-Typo" description="All blog posts from the Volks-Typo theme">
  <div class="blog-page">
    <header class="blog-header">
      <h1 class="blog-title">Blog</h1>
      <p class="blog-description">
        Exploring design, typography, and the intersection of historical aesthetics with modern functionality.
      </p>
      <hr class="blog-rule" />
    </header>

    {sortedPosts.length > 0 ? (
      <div class="posts-list">
        {sortedPosts.map((post: any) => (
          <article class="post-item">
            <h2 class="post-title">
              <a href={`${base}/blog/${post.file.split('/').pop()?.replace(/\.(md|mdx)$/, '')}`}>{post.frontmatter.title}</a>
            </h2>
            
            <div class="post-meta">
              <time class="post-date" datetime={post.frontmatter.date}>
                {new Date(post.frontmatter.date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
              
              <span class="post-reading-time">5 min read</span>
              
              {post.frontmatter.categories && (
                <div class="post-categories">
                  {post.frontmatter.categories.map((category: string) => (
                    <span class="category">{category}</span>
                  ))}
                </div>
              )}
            </div>
            
            {post.frontmatter.excerpt && (
              <p class="post-excerpt">{post.frontmatter.excerpt}</p>
            )}
            
            <a href={`${base}/blog/${post.file.split('/').pop()?.replace(/\.(md|mdx)$/, '')}`} class="read-more">Read More →</a>
          </article>
        ))}
      </div>
    ) : (
      <div class="no-posts">
        <p>No posts available yet. Start writing by creating files in <code>src/pages/blog/</code></p>
      </div>
    )}
  </div>
</Layout>

<style>
  .blog-page {
    max-width: 800px;
    margin: 0 auto;
  }

  .blog-header {
    text-align: center;
    margin-bottom: calc(var(--grid-unit) * 8);
  }

  .blog-title {
    font-family: var(--font-heading-primary);
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.1;
    margin-bottom: calc(var(--grid-unit) * 3);
  }

  .blog-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 4);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }


  .blog-rule {
    height: 4px;
    background-color: var(--color-accent);
    border: none;
    width: 120px;
    margin: 0 auto;
  }

  .posts-list {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) * 6);
  }

  .post-item {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: calc(var(--grid-unit) * 4);
  }

  .post-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .post-title {
    font-family: var(--font-heading-secondary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .post-title a {
    color: var(--color-text-primary);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .post-title a:hover,
  .post-title a:focus {
    color: var(--color-accent);
  }

  .post-meta {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit));
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .post-date {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .post-reading-time {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .post-reading-time::before {
    content: "•";
    margin: 0 calc(var(--grid-unit));
    color: var(--color-border);
  }

  .post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) / 2);
  }

  .category {
    background-color: var(--color-text-primary);
    color: white;
    padding: calc(var(--grid-unit) / 4) calc(var(--grid-unit) / 2);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 2px;
  }

  /* Dark mode category styles */
  :global([data-theme="dark"]) .category {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }

  .post-excerpt {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: calc(var(--grid-unit) * 2);
    font-size: 1.1rem;
  }

  .read-more {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-accent);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
  }

  .read-more:hover,
  .read-more:focus {
    color: var(--color-text-primary);
  }

  .no-posts {
    text-align: center;
    padding: calc(var(--grid-unit) * 6);
    color: var(--color-text-muted);
    font-style: italic;
  }

  .no-posts code {
    background-color: var(--color-surface);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--font-mono);
  }

  @media (max-width: 768px) {
    .blog-title {
      font-size: 2.5rem;
    }

    .blog-description {
      font-size: 1.125rem;
    }

    .post-title {
      font-size: 1.75rem;
    }

    .post-meta {
      flex-direction: column;
      align-items: flex-start;
    }
  }
</style>