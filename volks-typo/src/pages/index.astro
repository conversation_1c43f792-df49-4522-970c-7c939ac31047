---
import Layout from '../components/Layout.astro';
const mdPostsGlob = import.meta.glob('../content/blog/*.md', { eager: true });
const mdxPostsGlob = import.meta.glob('../content/blog/*.mdx', { eager: true });

const allPosts = [...Object.values(mdPostsGlob), ...Object.values(mdxPostsGlob)] as any[];
const sortedPosts = allPosts
  .sort((a: any, b: any) => new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime())
  .slice(0, 3);
---

<Layout title="Volks-Typo: Bauhaus Meets Monumental Design" description="An Astro blog theme exploring the aesthetic tension between Bauhaus modernism and WW2-era monumental design. Where functionalism meets grandeur in dissonant harmony." showSidebar={false}>
  <div class="landing-page">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">Volks-Typo</h1>
        <p class="hero-subtitle">Bauhaus Meets Monumental</p>
        <p class="hero-description">
          An aesthetic exploration where Bauhaus functionalism collides with 
          monumental grandeur. Form follows function, with a twist.
        </p>
        <hr class="hero-rule" />
        <div class="hero-actions">
          <a href="#demo" class="btn btn-primary">View Demo</a>
          <a href="#installation" class="btn btn-secondary">Get Started</a>
        </div>
      </div>
    </section>

    <!-- Demo Section -->
    <section class="demo-section" id="demo">
      <div class="container">
        <h2 class="section-title">Live Demo</h2>
        <p class="demo-description">
          See the theme in action with sample blog posts that demonstrate all typography, 
          layout, and design elements working together in perfect dissonant harmony.
        </p>
        
        {sortedPosts.length > 0 && (
          <div class="demo-posts">
            {sortedPosts.map(post => (
              <article class="demo-post-card">
                <h3 class="demo-post-title">
                  <a href={post.url}>{post.frontmatter.title}</a>
                </h3>
                
                <div class="demo-post-meta">
                  <time class="demo-post-date" datetime={post.frontmatter.date}>
                    {new Date(post.frontmatter.date).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                  <span class="demo-post-reading-time">5 min read</span>
                </div>
                
                {post.frontmatter.excerpt && (
                  <p class="demo-post-excerpt">{post.frontmatter.excerpt}</p>
                )}
                
                {post.frontmatter.categories && (
                  <div class="demo-post-categories">
                    {post.frontmatter.categories.map((category: string) => (
                      <span class="demo-category">{category}</span>
                    ))}
                  </div>
                )}
              </article>
            ))}
          </div>
        )}
        
        <div class="demo-actions">
          <a href={`${import.meta.env.BASE_URL}blog`} class="btn btn-primary">View Blog</a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
      <div class="container">
        <h2 class="section-title">Design Philosophy</h2>
        <div class="features-grid">
          <div class="feature-card">
            <h3 class="feature-title">Dissonant Harmony</h3>
            <p class="feature-description">Bauhaus minimalism meets monumental design. Clean aesthetics with bold, commanding presence.</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">Perfect Readability</h3>
            <p class="feature-description">Optimized typography with ideal line lengths and spacing for maximum comprehension.</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">Left Aligned</h3>
            <p class="feature-description">Natural reading flow with consistent left alignment throughout all content.</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">Typographic Tension</h3>
            <p class="feature-description">Cormorant Garamond meets Inter. Classical serif headings contrast modern sans-serif body.</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">Constructivist Grid</h3>
            <p class="feature-description">8-point grid system creates structural order. Blood-red rules command attention.</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">Fast Performance</h3>
            <p class="feature-description">Zero JavaScript, minimal CSS, self-hosted fonts for blazing speed.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Typography Showcase -->
    <section class="typography-showcase">
      <div class="container">
        <h2 class="section-title">Typography</h2>
        <p class="typography-description">
          Work Sans provides exceptional readability across all text sizes and contexts
        </p>
        
        <div class="typography-samples">
          <div class="type-sample">
            <h1 class="sample-h1">MONUMENTAL TYPE</h1>
            <p class="type-meta">Cormorant Garamond • 900 Weight • Blood Red</p>
          </div>
          
          <div class="type-sample">
            <p class="sample-body">Bauhaus-inspired body text in Inter creates perfect readability. Function meets form in every letterform, optimized for modern screens.</p>
            <p class="type-meta">Inter • 400 Weight • Charcoal Black</p>
          </div>
          
          <div class="type-sample">
            <code class="sample-code">npm install volks-typo</code>
            <p class="type-meta">JetBrains Mono • Monospace Code</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Installation Section -->
    <section class="installation" id="installation">
      <div class="container">
        <h2 class="section-title">Quick Start</h2>
        <div class="install-steps">
          <div class="step">
            <h3 class="step-number">01</h3>
            <div class="step-content">
              <h4 class="step-title">Clone Repository</h4>
              <pre class="code-block"><code>git clone https://github.com/yourusername/volks-typo.git
cd volks-typo</code></pre>
            </div>
          </div>
          
          <div class="step">
            <h3 class="step-number">02</h3>
            <div class="step-content">
              <h4 class="step-title">Install Dependencies</h4>
              <pre class="code-block"><code>npm install</code></pre>
            </div>
          </div>
          
          <div class="step">
            <h3 class="step-number">03</h3>
            <div class="step-content">
              <h4 class="step-title">Start Development</h4>
              <pre class="code-block"><code>npm run dev</code></pre>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</Layout>

<style>
  .landing-page {
    width: 100%;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--content-padding);
  }

  .hero {
    text-align: center;
    padding: calc(var(--grid-unit) * 12) 0 calc(var(--grid-unit) * 8);
    background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-background) 100%);
  }

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--content-padding);
  }

  .hero-title {
    font-family: var(--font-heading-primary);
    font-size: 4.5rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.15em;
    line-height: 0.9;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .hero-subtitle {
    font-family: var(--font-heading-secondary);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.2em;
    margin-bottom: calc(var(--grid-unit) * 3);
  }

  .hero-description {
    font-size: 1.375rem;
    line-height: 1.6;
    color: var(--color-text-primary);
    margin-bottom: calc(var(--grid-unit) * 4);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .hero-rule {
    height: 6px;
    background-color: var(--color-accent);
    border: none;
    width: 150px;
    margin: 0 auto calc(var(--grid-unit) * 4);
  }

  .hero-actions {
    display: flex;
    gap: calc(var(--grid-unit) * 2);
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: calc(var(--grid-unit) * 1.5) calc(var(--grid-unit) * 3);
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    font-size: 0.95rem;
  }

  .btn-primary {
    background-color: var(--color-accent);
    color: white;
    border: 2px solid var(--color-accent);
  }

  .btn-primary:hover,
  .btn-primary:focus {
    background-color: transparent;
    color: var(--color-accent);
    transform: translateY(-2px);
  }

  .btn-secondary {
    background-color: transparent;
    color: var(--color-text-primary);
    border: 2px solid var(--color-border);
  }

  .btn-secondary:hover,
  .btn-secondary:focus {
    background-color: var(--color-text-primary);
    color: var(--color-background);
    transform: translateY(-2px);
  }

  /* Dark mode button styles */
  :global([data-theme="dark"]) .btn-primary {
    background-color: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
  }

  :global([data-theme="dark"]) .btn-primary:hover,
  :global([data-theme="dark"]) .btn-primary:focus {
    background-color: transparent;
    color: var(--color-accent);
  }

  :global([data-theme="dark"]) .btn-secondary {
    background-color: transparent;
    color: var(--color-text-primary);
    border-color: var(--color-border);
  }

  :global([data-theme="dark"]) .btn-secondary:hover,
  :global([data-theme="dark"]) .btn-secondary:focus {
    background-color: var(--color-accent);
    color: white;
    border-color: var(--color-accent);
  }

  .features {
    padding: calc(var(--grid-unit) * 8) 0;
  }

  .section-title {
    font-family: var(--font-heading-secondary);
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: calc(var(--grid-unit) * 6);
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: calc(var(--grid-unit) * 4);
  }

  .feature-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: calc(var(--grid-unit) * 4);
    text-align: center;
    transition: transform 0.3s ease, border-color 0.3s ease;
  }

  .feature-card:hover {
    transform: translateY(-4px);
    border-color: var(--color-accent);
  }

  .feature-icon {
    font-size: 3rem;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .feature-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: calc(var(--grid-unit) * 1.5);
  }

  .feature-description {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin: 0;
  }

  /* Typography Showcase */
  .typography-showcase {
    padding: calc(var(--grid-unit) * 8) 0;
    background-color: var(--color-surface);
  }

  .typography-description {
    text-align: center;
    font-size: 1.125rem;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .typography-samples {
    display: grid;
    gap: calc(var(--grid-unit) * 6);
  }

  .type-sample {
    text-align: center;
    padding: calc(var(--grid-unit) * 4);
    background-color: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: 8px;
  }

  .sample-h1 {
    font-family: var(--font-heading-primary);
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.15em;
    line-height: 1.1;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .sample-h2 {
    font-family: var(--font-heading-secondary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    line-height: 1.2;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .sample-body {
    font-family: var(--font-body);
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--color-text-primary);
    margin-bottom: calc(var(--grid-unit) * 2);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .sample-code {
    font-family: var(--font-mono);
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    padding: calc(var(--grid-unit)) calc(var(--grid-unit) * 1.5);
    border-radius: 4px;
    display: inline-block;
    margin-bottom: calc(var(--grid-unit) * 2);
    color: var(--color-text-primary);
  }

  .type-meta {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
  }

  .demo-section {
    padding: calc(var(--grid-unit) * 8) 0;
  }

  .demo-description {
    text-align: center;
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 6);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .demo-posts {
    display: grid;
    gap: calc(var(--grid-unit) * 4);
    margin-bottom: calc(var(--grid-unit) * 6);
  }

  .demo-post-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    padding: calc(var(--grid-unit) * 3);
    transition: transform 0.2s ease, border-color 0.2s ease;
  }

  .demo-post-card:hover {
    transform: translateY(-2px);
    border-color: var(--color-accent);
  }

  .demo-post-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: calc(var(--grid-unit));
  }

  .demo-post-title a {
    color: var(--color-text-primary);
    text-decoration: none;
  }

  .demo-post-title a:hover,
  .demo-post-title a:focus {
    color: var(--color-accent);
  }

  .demo-post-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit));
    margin-bottom: calc(var(--grid-unit) * 1.5);
  }

  .demo-post-date {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .demo-post-reading-time {
    font-family: var(--font-mono);
    font-size: 0.85rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .demo-post-reading-time::before {
    content: "•";
    margin: 0 calc(var(--grid-unit) / 2);
    color: var(--color-border);
  }

  .demo-post-excerpt {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .demo-post-categories {
    display: flex;
    flex-wrap: wrap;
    gap: calc(var(--grid-unit) / 2);
  }

  .demo-category {
    background-color: var(--color-text-primary);
    color: white;
    padding: calc(var(--grid-unit) / 4) calc(var(--grid-unit) / 2);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: 2px;
  }

  /* Dark mode demo category styles */
  :global([data-theme="dark"]) .demo-category {
    background-color: var(--color-surface);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border);
  }

  .demo-actions {
    text-align: center;
  }

  .installation {
    padding: calc(var(--grid-unit) * 8) 0;
    background-color: var(--color-surface);
  }

  .install-steps {
    display: grid;
    gap: calc(var(--grid-unit) * 4);
  }

  .step {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: calc(var(--grid-unit) * 2);
    align-items: start;
  }

  .step-number {
    font-family: var(--font-heading-primary);
    font-size: 3rem;
    font-weight: 900;
    color: var(--color-accent);
    margin: 0;
  }

  .step-content {
    display: flex;
    flex-direction: column;
  }

  .step-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: calc(var(--grid-unit));
  }

  .code-block {
    background-color: var(--color-text-primary);
    color: var(--color-background);
    padding: calc(var(--grid-unit) * 2);
    border-radius: 8px;
    overflow-x: auto;
    margin: 0;
  }

  .code-block code {
    font-family: var(--font-mono);
    background: none;
    border: none;
    color: inherit;
  }

  @media (max-width: 768px) {
    .hero-title {
      font-size: 3rem;
    }

    .hero-subtitle {
      font-size: 1.25rem;
    }

    .hero-description {
      font-size: 1.125rem;
    }

    .hero-actions {
      flex-direction: column;
      align-items: center;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .step {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .step-number {
      font-size: 2rem;
    }
  }
</style>