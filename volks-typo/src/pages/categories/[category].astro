---
import Layout from '../../components/Layout.astro';

export async function getStaticPaths() {
  const postsGlob = import.meta.glob('../../content/blog/*.md', { eager: true });
  const allPosts = Object.values(postsGlob) as any[];
  
  // Extract all unique categories
  const allCategories = new Set<string>();
  allPosts.forEach((post: any) => {
    if (post.frontmatter.categories) {
      post.frontmatter.categories.forEach((category: string) => {
        allCategories.add(category);
      });
    }
  });

  // Create paths for each category
  return Array.from(allCategories).map((category: string) => {
    const categorySlug = category.toLowerCase().replace(/[\/\s]+/g, '-');
    const postsInCategory = allPosts.filter((post: any) => 
      post.frontmatter.categories && post.frontmatter.categories.includes(category)
    ).sort((a: any, b: any) => new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime());

    return {
      params: { category: categorySlug },
      props: {
        category,
        posts: postsInCategory
      }
    };
  });
}

const { category, posts } = Astro.props;
const base = import.meta.env.BASE_URL;
---

<Layout title={`${category} - Volks-Typo`} description={`All posts in the ${category} category`}>
  <div class="category-page">
    <header class="page-header">
      <h1 class="page-title">{category}</h1>
      <p class="page-description">
        {posts.length} article{posts.length === 1 ? '' : 's'} in this category
      </p>
      <hr class="page-rule" />
    </header>

    <div class="posts-list">
      {posts.map((post: any) => (
        <article class="post-item">
          <h2 class="post-title">
            <a href={`${base}blog/${post.file.split('/').pop()?.replace('.md', '')}`}>
              {post.frontmatter.title}
            </a>
          </h2>
          
          <div class="post-meta">
            <time class="post-date" datetime={post.frontmatter.date}>
              {new Date(post.frontmatter.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </time>
          </div>
          
          {post.frontmatter.excerpt && (
            <p class="post-excerpt">{post.frontmatter.excerpt}</p>
          )}
          
          <a href={`${base}blog/${post.file.split('/').pop()?.replace('.md', '')}`} class="read-more">
            Read More →
          </a>
        </article>
      ))}
    </div>

    <div class="page-navigation">
      <a href={`${base}categories`} class="back-link">← Back to All Categories</a>
    </div>
  </div>
</Layout>

<style>
  .category-page {
    max-width: 800px;
    margin: 0 auto;
  }

  .page-header {
    text-align: center;
    margin-bottom: calc(var(--grid-unit) * 8);
  }

  .page-title {
    font-family: var(--font-heading-primary);
    font-size: 3.5rem;
    font-weight: 900;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.1;
    margin-bottom: calc(var(--grid-unit) * 3);
  }

  .page-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin-bottom: calc(var(--grid-unit) * 4);
  }

  .page-rule {
    height: 4px;
    background-color: var(--color-accent);
    border: none;
    width: 120px;
    margin: 0 auto;
  }

  .posts-list {
    display: flex;
    flex-direction: column;
    gap: calc(var(--grid-unit) * 6);
    margin-bottom: calc(var(--grid-unit) * 8);
  }

  .post-item {
    border-bottom: 1px solid var(--color-border);
    padding-bottom: calc(var(--grid-unit) * 4);
  }

  .post-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .post-title {
    font-family: var(--font-heading-secondary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .post-title a {
    color: var(--color-text-primary);
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .post-title a:hover,
  .post-title a:focus {
    color: var(--color-accent);
  }

  .post-meta {
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .post-date {
    font-family: var(--font-mono);
    font-size: 0.9rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .post-excerpt {
    color: var(--color-text-secondary);
    line-height: 1.6;
    margin-bottom: calc(var(--grid-unit) * 2);
    font-size: 1.1rem;
  }

  .read-more {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-accent);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
  }

  .read-more:hover,
  .read-more:focus {
    color: var(--color-text-primary);
  }

  .page-navigation {
    text-align: center;
    padding-top: calc(var(--grid-unit) * 4);
    border-top: 1px solid var(--color-border);
  }

  .back-link {
    font-family: var(--font-heading-secondary);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-accent);
    padding: calc(var(--grid-unit)) calc(var(--grid-unit) * 2);
    border: 2px solid var(--color-accent);
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
  }

  .back-link:hover,
  .back-link:focus {
    background-color: var(--color-accent);
    color: white;
    text-decoration: none;
  }

  @media (max-width: 768px) {
    .page-title {
      font-size: 2.5rem;
    }

    .post-title {
      font-size: 1.75rem;
    }
  }
</style>