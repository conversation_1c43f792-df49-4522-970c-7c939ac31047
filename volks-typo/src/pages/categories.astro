---
import Layout from '../components/Layout.astro';

const base = import.meta.env.BASE_URL;

// Get all blog posts
const postsGlob = import.meta.glob('../content/blog/*.md', { eager: true });
const allPosts = Object.values(postsGlob) as any[];

// Extract all categories from posts
const allCategories = new Set<string>();
allPosts.forEach((post: any) => {
  if (post.frontmatter.categories) {
    post.frontmatter.categories.forEach((category: string) => {
      allCategories.add(category);
    });
  }
});

// Create category map with post counts
const categoryMap: Record<string, any[]> = {};
Array.from(allCategories).forEach((category: string) => {
  categoryMap[category] = allPosts.filter((post: any) => 
    post.frontmatter.categories && post.frontmatter.categories.includes(category)
  );
});

// Sort categories by post count (descending) then alphabetically
const sortedCategories = Object.entries(categoryMap)
  .sort((a: any, b: any) => {
    if (b[1].length !== a[1].length) {
      return b[1].length - a[1].length;
    }
    return a[0].localeCompare(b[0]);
  });
---

<Layout title="Categories - Volks-Typo" description="Browse articles by category" showSidebar={true}>
  <div class="categories-page">
    <header class="page-header">
      <h1 class="page-title">Categories</h1>
      <p class="page-description">
        Browse articles organized by category. Click on any category to see related posts.
      </p>
      <hr class="page-rule" />
    </header>

    <div class="categories-grid">
      {sortedCategories.map(([category, posts]: [string, any[]]) => (
        <div class="category-card">
          <a href={`${base}categories/${category.toLowerCase().replace(/[\/\s]+/g, '-')}`} class="category-link">
            <h2 class="category-title">{category}</h2>
            <p class="category-count">
              {posts.length} article{posts.length === 1 ? '' : 's'}
            </p>
          </a>
        </div>
      ))}
    </div>

    {sortedCategories.length === 0 && (
      <div class="no-categories">
        <p>No categories found. Add categories to your blog posts in the frontmatter.</p>
      </div>
    )}
  </div>
</Layout>

<style>
  .categories-page {
    max-width: 100%;
  }

  .page-header {
    text-align: center;
    margin-bottom: calc(var(--grid-unit) * 6);
  }

  .page-title {
    font-family: var(--font-heading-primary);
    font-size: 3rem;
    font-weight: 700;
    color: var(--color-accent);
    text-transform: uppercase;
    letter-spacing: 0.15em;
    margin-bottom: calc(var(--grid-unit) * 2);
  }

  .page-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto calc(var(--grid-unit) * 3);
  }

  .page-rule {
    height: 4px;
    background-color: var(--color-accent);
    border: none;
    width: 120px;
    margin: 0 auto;
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: calc(var(--grid-unit) * 2);
  }

  .category-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 6px;
    transition: transform 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
  }

  .category-card:hover {
    transform: translateY(-2px);
    border-color: var(--color-accent);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .category-link {
    display: block;
    padding: calc(var(--grid-unit) * 2);
    text-decoration: none;
    color: inherit;
  }

  .category-title {
    font-family: var(--font-heading-secondary);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: calc(var(--grid-unit) / 2);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--color-text-primary);
    transition: color 0.2s ease;
  }

  .category-link:hover .category-title {
    color: var(--color-accent);
  }

  .category-count {
    font-family: var(--font-mono);
    font-size: 0.75rem;
    color: var(--color-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
  }

  .no-categories {
    text-align: center;
    padding: calc(var(--grid-unit) * 6);
    color: var(--color-text-muted);
    font-style: italic;
  }

  @media (max-width: 768px) {
    .page-title {
      font-size: 2.5rem;
    }

    .categories-grid {
      grid-template-columns: 1fr;
    }
  }
</style>