---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. macOS, Windows, Linux]
 - Browser: [e.g. Chrome, Safari, Firefox]
 - Browser Version: [e.g. 22]
 - Node Version: [e.g. 18.x]
 - Astro Version: [e.g. 4.x]

**Desktop (please complete the following information):**
 - Device: [e.g. MacBook Pro]
 - Resolution: [e.g. 1920x1080]

**Smartphone (please complete the following information):**
 - Device: [e.g. iPhone 14]
 - OS: [e.g. iOS 17.0]
 - Browser: [e.g. Safari]
 - Version: [e.g. 17]

**Additional context**
Add any other context about the problem here.

**Possible Solution**
If you have any ideas on how to fix this issue, please describe them here.

**Related Issues**
Link to any related issues here.

**Checklist**
- [ ] I have searched the existing issues to make sure this is not a duplicate
- [ ] I have provided all the required information above
- [ ] I have tested with the latest version of the theme
- [ ] I have cleared my browser cache and tried in incognito/private mode