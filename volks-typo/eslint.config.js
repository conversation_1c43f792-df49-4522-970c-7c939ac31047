import eslintPlugin<PERSON>tro from 'eslint-plugin-astro';
import tseslint from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';

export default [
  // Base config for all files
  {
    ignores: ['dist/', 'node_modules/', '.astro/']
  },
  
  // JavaScript/TypeScript files
  {
    files: ['**/*.js', '**/*.ts', '**/*.mjs'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module'
      }
    },
    plugins: {
      '@typescript-eslint': tseslint
    },
    rules: {
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'off',
      'no-console': 'warn'
    }
  },
  
  // Astro files
  ...eslintPluginAstro.configs.recommended,
  {
    files: ['**/*.astro'],
    rules: {
      // Custom Astro rules if needed
      'astro/no-set-html-directive': 'off'
    }
  }
];