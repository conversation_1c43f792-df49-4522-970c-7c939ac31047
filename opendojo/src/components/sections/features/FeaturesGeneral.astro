---
// Import the necessary dependencies
import { Image } from "astro:assets";
import IconBlock from "@components/ui/blocks/IconBlock.astro";
import Icon from "@components/ui/icons/Icon.astro";

interface Feature {
  heading: string;
  content: string;
  svg: string;
}

interface Props {
  title?: string;
  subTitle?: string;
  features?: Feature[];
  src?: any;
  alt?: string;
}
// Define props from Astro
const { title, subTitle, src, alt, features } = Astro.props;
---

<section
  class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
>
  <!-- Block to display the feature image -->
  <div class="relative mb-6 overflow-hidden md:mb-8">
    {
      src && alt && (
        <Image
          src={src}
          alt={alt}
          class="h-full w-full object-cover object-center"
          draggable={"false"}
          format={"avif"}
          loading={"eager"}
        />
      )
    }
  </div>

  <!-- Displaying the main content consisting of title, subtitle, and several `IconBlock` components -->
  <div class="mt-5 grid gap-8 lg:mt-16 lg:grid-cols-3 lg:gap-12">
    <!-- Block for title and subtitle -->
    <div class="lg:col-span-1">
      <!-- Rendering title -->
      <h2
        class="text-balance text-2xl font-bold text-neutral-800 dark:text-neutral-200 md:text-3xl"
      >
        {title}
      </h2>
      <!-- Rendering subtitle -->
      {
        subTitle && (
          <p class="mt-2 text-pretty text-neutral-600 dark:text-neutral-400 md:mt-4">
            {subTitle}
          </p>
        )
      }
    </div>

    <!-- Block to display the IconBlock components -->
    <div class="lg:col-span-2">
      <div class="grid gap-8 sm:grid-cols-2 md:gap-12">
        <!-- Injecting IconBlock components with different properties -->
        { features &&
          features.map((feature) => (
            <IconBlock heading={feature.heading} content={feature.content}>
              <Icon name={feature.svg} />
            </IconBlock>
          ))
        }
      </div>
    </div>
  </div>
</section>
