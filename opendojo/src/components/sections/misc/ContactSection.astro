---
// Import the necessary dependencies.
import AuthBtn from "@components/ui/buttons/AuthBtn.astro";
import ContactIconBlock from "@components/ui/blocks/ContactIconBlock.astro";
import TextInput from "@components/ui/forms/input/TextInput.astro";
import EmailContactInput from "@components/ui/forms/input/EmailContactInput.astro";
import PhoneInput from "@components/ui/forms/input/PhoneInput.astro";
import TextAreaInput from "@components/ui/forms/input/TextAreaInput.astro";
import Icon from "@components/ui/icons/Icon.astro";

// Define the variables that will be used in this component
const title: string = "Contact us";
const subTitle: string =
  "Have questions or want to discuss a project? Reach out, and let's craft the perfect solution with our tools and services.";
const formTitle: string = "Fill in the form below";
const formSubTitle: string = "We'll get back to you in 1-2 business days.";
---

<!-- Contact Us -->
<section class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14">
  <div class="mx-auto max-w-2xl lg:max-w-5xl">
    <div class="text-center">
      <h1
        class="text-balance text-2xl font-bold tracking-tight text-neutral-800 dark:text-neutral-200 md:text-4xl md:leading-tight"
      >
        {title}
      </h1>
      <p class="mt-1 text-pretty text-neutral-600 dark:text-neutral-400">
        {subTitle}
      </p>
    </div>

    <div class="mt-12 grid items-center gap-6 lg:grid-cols-2 lg:gap-16">
      <div class="flex flex-col rounded-xl p-4 sm:p-6 lg:p-8">
        <h2
          class="mb-8 text-xl font-bold text-neutral-700 dark:text-neutral-300"
        >
          {formTitle}
        </h2>
        <!-- Form for user input with various input fields.-->
        <!-- Each field utilizes a different input component for the specific type of input (text, email, phone, and textarea)-->
        <form>
          <div class="grid gap-4">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <TextInput
                id="hs-firstname-contacts"
                label="First Name"
                name="hs-firstname-contacts"
              />
              <TextInput
                id="hs-lastname-contacts"
                label="Last Name"
                name="hs-firstname-contacts"
              />
            </div>
            <EmailContactInput id="hs-email-contacts" />
            <PhoneInput id="hs-phone-number" />
            <TextAreaInput
              id="hs-about-contacts"
              label="Details"
              name="hs-about-contacts"
            />
          </div>

          <div class="mt-4 grid">
            <AuthBtn title="Send Message" />
          </div>

          <div class="mt-3 text-center">
            <p class="text-sm text-neutral-600 dark:text-neutral-400">
              {formSubTitle}
            </p>
          </div>
        </form>
      </div>

      <!--ContactIconBlocks are used to display different methods of contacting, including visiting office, email, browsing knowledgebase, and FAQ.-->
      <div class="divide-y divide-neutral-300 dark:divide-neutral-700">
        <ContactIconBlock
          heading="Knowledgebase"
          content="Browse through all of our knowledgebase articles."
          isLinkVisible={true}
          linkTitle="Visit guides & tutorials"
          linkURL="#"
          isArrowVisible={true}
          ><Icon name="question" />
        </ContactIconBlock>

        <ContactIconBlock
          heading="FAQ"
          content="Explore our FAQ for quick, clear answers to common queries."
          isLinkVisible={true}
          linkTitle="Visit FAQ"
          linkURL="#"
          isArrowVisible={true}
          ><Icon name="chatBubble" />
        </ContactIconBlock>

        <ContactIconBlock
          heading="Visit our office"
          content="UK ScrewFast"
          isAddressVisible={true}
          addressContent="72 Union Terrace, E10 4PE London"
          ><Icon name="mapPin" />
        </ContactIconBlock>

        <ContactIconBlock
          heading="Contact us by email"
          content="Prefer the written word? Drop us an email at"
          isLinkVisible={true}
          linkTitle="<EMAIL>"
          linkURL="#"
          ><Icon name="envelopeOpen" />
        </ContactIconBlock>
      </div>
    </div>
  </div>
</section>
