---
import { getImage } from "astro:assets";
import { OG, SEO, SITE } from "@data/constants";
import faviconSvgSrc from "@images/icon.svg";
import faviconSrc from "@images/icon.png";

// Default properties for the Meta component. These values are used if props are not provided.
// 'meta' sets a default description meta tag to describe the page content.
// 'structuredData' defines default structured data in JSON-LD format to enhance search engine understanding of the page (for SEO purposes).
const defaultProps = {
  meta: SITE.description,
  structuredData: SEO.structuredData,
};

// Extract props with default values assigned from defaultProps. Values can be overridden when the component is used.
// For example:
// <MainLayout title="Custom Title" meta="Custom description." />
const {
  meta = defaultProps.meta,
  structuredData = defaultProps.structuredData,
} = Astro.props;

// Define the metadata for your website and individual pages
const URL = `${Astro.site}`; // Set the website URL in astro.config.mjs
const author = SITE.author;
const canonical = Astro.url.href;
const basePath = Astro.url.pathname;
const ogTitle = OG.title;
const ogDescription = OG.description;
const socialImageRes = await getImage({
  src: OG.image,
  width: 1200,
  height: 600,
});
const socialImage = Astro.url.origin + socialImageRes.src; // Get the full URL of the image (https://stackoverflow.com/a/9858694)

const languages: { [key: string]: string } = {
  en: "",
  fr: "fr",
};

function createHref(lang: string, prefix: string, path: string): string {
  const hasPrefix = path.startsWith(`/${prefix}/`);

  const basePath = hasPrefix ? path : `/${prefix}${path}`;
  const normalizedBasePath = basePath.replace(/\/\/+/g, "/");
  return `${URL.slice(0, -1)}${normalizedBasePath}`;
}

const fullPath: string = Astro.url.pathname;

const alternateLanguageLinks: string = Object.entries(languages)
  .map(([lang, prefix]: [string, string]) => {
    const basePath: string =
      lang === "en" ? fullPath.replace(/^\/fr\//, "/") : fullPath;

    const href: string = createHref(lang, prefix, basePath);

    return `<link rel="alternate" hreflang="${lang}" href="${href}" />`;
  })
  .join("\n");

// Generate and optimize the favicon images
const faviconSvg = await getImage({
  src: faviconSvgSrc,
  format: "svg",
});

const appleTouchIcon = await getImage({
  src: faviconSrc,
  width: 180,
  height: 180,
  format: "png",
});
---

<!-- Inject structured data into the page if provided. This data is formatted as JSON-LD, a method recommended by Google for structured data pass:
     https://developers.google.com/search/docs/advanced/structured-data/intro-structured-data -->{
  structuredData && (
    <script
      type="application/ld+json"
      set:html={JSON.stringify(structuredData)}
    />
  )
}
<!-- Define the character set, description, author, and viewport settings -->
<meta charset="utf-8" />
<meta content={meta} name="description" />
<meta name="web_author" content={author} />
<meta
  name="viewport"
  content="width=device-width, initial-scale=1.0, maximum-scale=5.0, minimum-scale=1.0"
/>
<meta http-equiv="X-UA-Compatible" content="ie=edge" />
<link rel="canonical" href={canonical} />
<Fragment set:html={alternateLanguageLinks} />

<!-- Facebook Meta Tags -->
<meta property="og:locale" content="en_US" />
<meta property="og:url" content={URL} />
<meta property="og:type" content="website" />
<meta property="og:title" content={ogTitle} />
<meta property="og:site_name" content={SITE.title} />
<meta property="og:description" content={ogDescription} />
<meta property="og:image" content={socialImage} />
<meta content="1200" property="og:image:width" />
<meta content="600" property="og:image:height" />
<meta content="image/png" property="og:image:type" />

<!-- Twitter Meta Tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta property="twitter:domain" content={URL} />
<meta property="twitter:url" content={URL} />
<meta name="twitter:title" content={ogTitle} />
<meta name="twitter:description" content={ogDescription} />
<meta name="twitter:image" content={socialImage} />

<!-- Links to the webmanifest and sitemap -->
<link rel="manifest" href="/manifest.json" />
<!-- https://docs.astro.build/en/guides/integrations-guide/sitemap/ -->
<link rel="sitemap" href="/sitemap-index.xml" />

<!-- Links for favicons -->
<link href="/favicon.ico" rel="icon" sizes="any" type="image/x-icon" />
<link href={faviconSvg.src} rel="icon" type="image/svg+xml" sizes="any" />
<meta name="mobile-web-app-capable" content="yes" />
<link href={appleTouchIcon.src} rel="apple-touch-icon" />
<link href={appleTouchIcon.src} rel="shortcut icon" />
<!-- Set theme color -->
<meta name="theme-color" content="#facc15" />
