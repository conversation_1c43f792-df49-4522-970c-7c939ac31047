---
// Import necessary components and utilities
import AvatarBlog from "@components/ui/avatars/AvatarBlog.astro";
import { Image } from "astro:assets";
import { formatDate } from "@utils/utils";
import type { CollectionEntry } from "astro:content";

const { blogEntry, blogLocale = "" } = Astro.props;

interface Props {
  blogEntry: CollectionEntry<"blog">;
  blogLocale?: string;
}
---

<!-- The following anchor tag is the main container for the card.
     It's a link to the blog post detailed page.
     The data-astro-prefetch is an Astro specific Dynamic HTML feature,
     which automatically prefetches the linked page to speed up navigation. -->
<a
  class="group relative block rounded-xl outline-hidden ring-zinc-500 transition duration-500 focus-visible:ring-3 dark:ring-zinc-200 dark:focus:outline-hidden"
  href={blogLocale && blogLocale !== "en"
    ? `/${blogLocale}/blog/${blogEntry.id.replace(/^fr\//, "")}/`
    : `/blog/${blogEntry.id.replace(/^en\//, "")}/`}
  data-astro-prefetch
>
  <!-- The container for the blog post's cover image. Uses astro:assets' Image for image source -->
  <div
    class="relative h-[350px] w-full shrink-0 overflow-hidden rounded-xl before:absolute before:inset-x-0 before:z-1 before:size-full before:bg-linear-to-t before:from-neutral-900/[.7]"
  >
    <Image
      class="absolute start-0 top-0 size-full object-cover transition duration-500 group-hover:scale-110"
      src={blogEntry.data.cardImage}
      alt={blogEntry.data.cardImageAlt}
      draggable={"false"}
      loading={"eager"}
      format={"avif"}
    />
  </div>
  <!-- The container for the blog author's avatar and associated metadata (author name and publication date) -->
  <div class="absolute inset-x-0 top-0 z-10">
    <div class="flex h-full flex-col p-4 sm:p-6">
      <div class="flex items-center">
        <AvatarBlog blogEntry={blogEntry} />

        <div class="ms-2.5 sm:ms-4">
          <h4 class="font-bold text-neutral-50">
            {blogEntry.data.author}
          </h4>
          <p class="text-xs text-neutral-50/[.8]">
            {formatDate(blogEntry.data.pubDate)}
          </p>
        </div>
      </div>
    </div>
  </div>
  <!-- The container for the blog post's title and description -->
  <div class="absolute inset-x-0 bottom-0 z-10">
    <div class="flex h-full flex-col p-4 sm:p-6">
      <h3
        class="text-balance text-lg font-bold text-neutral-50 group-hover:text-neutral-50/[.8] sm:text-3xl"
      >
        {blogEntry.data.title}
      </h3>
      <p class="mt-2 text-pretty text-neutral-50/[.8]">
        {blogEntry.data.description}
      </p>
    </div>
  </div>
</a>
