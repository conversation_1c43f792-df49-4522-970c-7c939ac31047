---
// Import necessary modules and utilities
import { Image } from "astro:assets";
import Icon from "@components/ui/icons/Icon.astro";
import type { CollectionEntry } from "astro:content";

const {
  insightEntry,
  insightLocale,
  label = Astro.currentLocale === "fr" ? "Lire plus" : "Read more",
} = Astro.props;

interface Props {
  insightEntry: CollectionEntry<"insights">;
  insightLocale?: string;
  label?: string;
}
---

<!-- The anchor tag is the root container for the "Insight" card. It links to the insight detail page. -->
<a
  class="group rounded-xl outline-hidden ring-zinc-500 transition duration-300 focus-visible:ring-3 dark:ring-zinc-200 dark:focus:outline-hidden"
  href={insightLocale && insightLocale !== "en"
    ? `/${insightLocale}/insights/${insightEntry.id.replace(/^fr\//, "")}/`
    : `/insights/${insightEntry.id.replace(/^en\//, "")}/`}
>
  <!-- This is the container for the insight's cover image. -->
  <div class="relative overflow-hidden rounded-xl pt-[50%] sm:pt-[70%]">
    <Image
      class="absolute start-0 top-0 size-full rounded-xl object-cover transition duration-500 ease-in-out group-hover:scale-105"
      src={insightEntry.data.cardImage}
      alt={insightEntry.data.cardImageAlt}
      draggable={"false"}
      format={"avif"}
    />
  </div>
  <!-- This is the container for the insight's title and description. -->
  <div class="mt-7">
    <!-- The title of the insight -->
    <h3
      class="text-xl font-bold text-neutral-800 group-hover:text-neutral-600 dark:text-neutral-200 dark:group-hover:text-neutral-400"
    >
      {insightEntry.data.title}
    </h3>
    <!-- The description of the insight -->
    <p class="mt-3 text-neutral-600 dark:text-neutral-400">
      {insightEntry.data.description}
    </p>
    <!-- The "Read More" hyperlink going to the full insight. With an arrow icon -->
    <p
      class="mt-5 inline-flex items-center gap-x-1 font-medium text-orange-400 decoration-2 group-hover:underline dark:text-orange-300"
    >
      {label}
      <Icon name="arrowRightStatic" />
    </p>
  </div>
</a>
