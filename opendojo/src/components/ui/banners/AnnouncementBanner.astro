---
const { title, btnId, btnTitle, url } = Astro.props;

interface Props {
  title?: string;
  btnId: string;
  btnTitle: string;
  url: string;
}
---

<astro-banner btnId={btnId}>
  <div
    class="fixed bottom-0 start-1/2 z-50 mx-auto w-full -translate-x-1/2 transform p-6 sm:max-w-4xl"
    role="region"
    aria-label="Informational Banner"
  >
    <div
      class="rounded-xl bg-neutral-800 bg-[url('/banner-pattern.svg')] bg-cover bg-center bg-no-repeat p-4 text-center shadow-xs dark:bg-neutral-200"
    >
      <div class="flex items-center justify-center">
        <div class="ml-auto">
          {title &&
          <p
            class="me-2 inline-block font-medium text-neutral-50 dark:text-neutral-700"
          >
            {title}
          </p>
          }
          <a
            class="group inline-flex items-center gap-x-2 rounded-full border-2 border-neutral-50 backdrop-brightness-75 sm:backdrop-brightness-100 px-3 py-2 text-sm font-semibold text-neutral-50 transition duration-300 hover:border-neutral-100/70 hover:text-neutral-50/70 disabled:pointer-events-none disabled:opacity-50 dark:backdrop-brightness-100 dark:border-neutral-700 dark:text-neutral-700 dark:hover:border-neutral-700/70 dark:hover:text-neutral-800/70 dark:focus:outline-hidden"
            href={url}
            target="_blank"
          >
            {btnTitle}
            <svg
              class="size-4 shrink-0 transition duration-300 group-hover:translate-x-1"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"><path d="m9 18 6-6-6-6"></path></svg
            >
          </a>
        </div>
        <button
          type="button"
          class="ml-auto inline-flex items-center gap-x-2 rounded-full border border-transparent bg-gray-100 p-2 text-sm font-semibold text-gray-800 hover:bg-gray-200 disabled:pointer-events-none disabled:opacity-50 dark:bg-neutral-700 dark:text-neutral-50 dark:hover:bg-neutral-700/80 dark:hover:text-neutral-50 dark:focus:outline-hidden"
          id={btnId}
        >
          <span class="sr-only">Dismiss</span>
          <svg
            class="size-5 shrink-0"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg
          >
        </button>
      </div>
    </div>
  </div>
</astro-banner>
<script>
  class AstroBanner extends HTMLElement {
    connectedCallback() {
      const btnId = this.getAttribute("btnId");
      const button = this.querySelector(`#${btnId}`);
      if (button != null) {
        button.addEventListener("click", () => this.remove());
      }
    }
  }

  customElements.define("astro-banner", AstroBanner);
</script>
