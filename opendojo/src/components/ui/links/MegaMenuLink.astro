---
import { Image } from "astro:assets";
import Icon from "../icons/Icon.astro";
import { servicesData, successStoriesData } from "@data/mega_link";
---

<div
  class="hs-dropdown py-3 [--adaptive:none] [--strategy:static] md:px-3 md:py-4 md:[--strategy:absolute] md:[--trigger:hover]"
>
  <button
    type="button"
    class="flex w-full text-base items-center font-medium text-neutral-600 hover:text-neutral-500 dark:text-neutral-400 dark:hover:text-neutral-500"
  >
    Services <Icon name="chevronDown" />
  </button>

  <div
    class="hs-dropdown-menu start-0 top-full z-10 hidden w-full min-w-60 rounded-2xl bg-neutral-50 py-2 opacity-0 transition-[opacity,margin] duration-[0.1ms] before:absolute before:-top-5 before:start-0 before:h-5 before:w-full hs-dropdown-open:opacity-100 dark:divide-neutral-700 dark:bg-neutral-800 md:p-4 md:shadow-2xl md:duration-[150ms]"
  >
    <div class="gap-4 md:grid md:grid-cols-2 lg:grid-cols-3">
      <div class="mx-1 flex flex-col md:mx-0">
        {
          servicesData &&
            servicesData.slice(0, 3).map((data) => (
              <a
                class="group flex gap-x-5 rounded-lg p-4 hover:bg-neutral-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10"
                href={data.url}
              >
                <Icon name={data.icon} class="mt-1 size-5 shrink-0" />
                <div class="grow">
                  <p class="font-medium text-neutral-800 dark:text-neutral-200">
                    {data.title}
                  </p>
                  <p class="text-sm text-neutral-500 group-hover:text-neutral-800 dark:text-neutral-400 dark:group-hover:text-neutral-200">
                    {data.description}
                  </p>
                </div>
              </a>
            ))
        }
      </div>

      <div class="mx-1 flex flex-col md:mx-0">
        {
          servicesData &&
            servicesData.slice(3, 6).map((data) => (
              <a
                class="group flex gap-x-5 rounded-lg p-4 hover:bg-neutral-100 dark:text-neutral-200 dark:hover:bg-neutral-500/10"
                href={data.url}
              >
                <Icon name={data.icon} class="mt-1 size-5 shrink-0" />
                <div class="grow">
                  <p class="font-medium text-neutral-800 dark:text-neutral-200">
                    {data.title}
                  </p>
                  <p class="text-sm text-neutral-500 group-hover:text-neutral-800 dark:text-neutral-400 dark:group-hover:text-neutral-200">
                    {data.description}
                  </p>
                </div>
              </a>
            ))
        }
      </div>

      <div class="mx-1 flex flex-col pt-4 md:mx-0 md:pt-0">
        <span
          class="text-sm font-semibold uppercase text-neutral-800 dark:text-neutral-200"
          >Success Stories</span
        >
        <a
          class="group mt-2 flex items-center gap-x-5 rounded-xl p-3 hover:bg-neutral-100 dark:hover:bg-neutral-500/10"
          href={successStoriesData[0].learnMoreUrl}
        >
          <Image
            src={successStoriesData[0].image}
            inferSize={true}
            alt={successStoriesData[0].alt}
            class="size-32 rounded-lg"
          />
          <div class="grow">
            <p class="text-sm text-neutral-800 dark:text-neutral-400">
              {successStoriesData[0].description}
            </p>
            <p
              class="mt-3 inline-flex items-center gap-x-1 text-sm font-medium text-orange-400 decoration-2 hover:underline dark:text-orange-300"
            >
              Learn more
              <Icon name="arrowRight" />
            </p>
          </div>
        </a>
      </div>
    </div>
  </div>
</div>
