---
import LanguageSelect from "@astrojs/starlight/components/LanguageSelect.astro";
import SocialIcons from "@astrojs/starlight/components/SocialIcons.astro";
import ThemeSelect from "src/components/ui/starlight/ThemeSelectMobile.astro";
import type { Props } from "@astrojs/starlight/props";
---

<div class="mobile-preferences sl-flex">
  <div class="sl-flex social-icons">
    <SocialIcons {...Astro.props}><slot /></SocialIcons>
  </div>
  <ThemeSelect />
  <LanguageSelect {...Astro.props}><slot /></LanguageSelect>
</div>

<style>
  .social-icons {
    margin-inline-end: auto;
    gap: 1rem;
    align-items: center;
    padding-block: 1rem;
  }
  .social-icons:empty {
    display: none;
  }
  .mobile-preferences {
    justify-content: space-between;
    flex-wrap: wrap;
    border-top: 1px solid var(--sl-color-gray-6);
    column-gap: 1rem;
    padding: 0.5rem 0;
    align-items: center;
  }
</style>
