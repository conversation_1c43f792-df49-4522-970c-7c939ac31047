---
import type { Props } from '@astrojs/starlight/props';
import StarlightHead from '@astrojs/starlight/components/Head.astro';
import VtbotStarlight from 'astro-vtbot/components/starlight/Base.astro';
---

<VtbotStarlight {...Astro.props} viewTransitionsFallback="animate">
  <StarlightHead {...Astro.props}><slot /></StarlightHead>
  <script>
    import "@scripts/lenisSmoothScroll.js";
  </script>
</VtbotStarlight>

<style is:global>
  /* Slow down Chrome's default animation */
  ::view-transition-group(root) {
    animation-duration: 250ms;
  }

  /* Do not slide over the sidebars */
  ::view-transition-group(*) {
    overflow: hidden;
  }
</style>
