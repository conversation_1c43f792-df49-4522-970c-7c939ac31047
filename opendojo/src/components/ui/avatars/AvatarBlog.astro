---
// Import necessary components
import { Image } from "astro:assets";

import type { CollectionEntry } from "astro:content";

const { blogEntry } = Astro.props;

interface Props {
  blogEntry: CollectionEntry<"blog">;
}
---

<div class="shrink-0">
  <Image
    class="size-[46px] rounded-full border-2 border-neutral-50"
    src={blogEntry.data.authorImage}
    alt={blogEntry.data.authorImageAlt}
    draggable={"false"}
    format={"avif"}
  />
</div>
