---
// Import necessary components from individual files
import EmailInput from "./input/EmailInput.astro";
import PasswordInput from "./input/PasswordInput.astro";
import Checkbox from "./input/Checkbox.astro";
import GoogleBtn from "@components/ui/buttons/GoogleBtn.astro";
import AuthBtn from "@components/ui/buttons/AuthBtn.astro";
// Config object for customization of the component
const config = {
  id: "hs-toggle-between-modals-register-modal", // Modal identifier
  title: "Sign up", // Main heading
  subTitle: "Already have an account?", // Sub-heading text
  loginBtn: "Sign in here", // Text for login button
  loginBtnDataHS: "#hs-toggle-between-modals-login-modal", // Target link for login button
};
---

<!-- Root element of the registration modal with the id and styling -->
<div
  id={config.id}
  class="hs-overlay absolute start-0 top-0 z-50 hidden h-full w-full hs-overlay-backdrop-open:bg-neutral-900/90"
>
  <div
    class="m-3 mt-0 opacity-0 transition-all ease-out hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 sm:mx-auto sm:w-full sm:max-w-lg"
  >
    <div class="mx-auto w-full max-w-md p-6">
      <div
        class="mt-7 max-h-full overflow-hidden rounded-xl border border-neutral-200 bg-neutral-100 shadow-xs dark:border-neutral-700 dark:bg-neutral-800"
      >
        <div class="p-4 sm:p-7">
          <div class="text-center">
            <h2
              class="block text-2xl font-bold text-neutral-800 dark:text-neutral-200"
            >
              {config.title}
            </h2>
            <p class="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
              {config.subTitle}
              <!-- Button to toggle login modal -->
              <button
                class="rounded-lg p-1 font-medium text-orange-400 decoration-2 outline-hidden ring-zinc-500 hover:underline focus-visible:ring-3 dark:text-orange-400 dark:ring-zinc-200 dark:focus:outline-hidden"
                data-hs-overlay={config.loginBtnDataHS}
              >
                {config.loginBtn}
              </button>
            </p>
          </div>
          <!-- The form for user registration -->
          <div class="mt-5">
            <!-- Google signup button -->
            <GoogleBtn title="Sign up with Google" />
            <!-- Dividing line with 'Or' text -->
            <div
              class="flex items-center py-3 text-xs uppercase text-neutral-400 before:me-6 before:flex-[1_1_0%] before:border-t before:border-neutral-200 after:ms-6 after:flex-[1_1_0%] after:border-t after:border-neutral-200 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600"
            >
              Or
            </div>
            <!-- Registration form -->
            <form>
              <div class="grid gap-y-4">
                <!-- Email input field -->
                <EmailInput id="register-email" errorId="register-email-error"/>
                <!-- Password input field -->
                <PasswordInput
                  id="create-password"
                  errorId="register-password-error"
                  content="8+ characters required"
                />
                <!-- Password confirmation input field -->
                <PasswordInput
                  label="Confirm Password"
                  id="confirm-password"
                  errorId="confirm-password-error"
                  content="Password does not match the password"
                />
                <!-- Checkbox with a label and a link for accepting the terms and conditions -->
                <Checkbox label="I accept the " , id="terms-agree">
                  <a
                    class="font-medium text-orange-400 decoration-2 hover:underline dark:text-orange-400 dark:focus:outline-hidden"
                    href="#">Terms and Conditions</a
                  >
                </Checkbox>
                <!-- Submit button for the registration form -->
                <AuthBtn title="Sign up" />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
