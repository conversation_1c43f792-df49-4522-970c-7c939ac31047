---
import Icon from "@components/ui/icons/Icon.astro";

const { count, description, index } = Astro.props;

interface Props {
  count: string;
  description: string;
  index: number;
}
---

<li class="-m-0.5 flex flex-col p-4 sm:p-8">
  <div
    class="mb-2 flex items-end gap-x-2 text-3xl font-bold text-neutral-800 dark:text-neutral-200 sm:text-5xl"
  >
    <!-- {index === 1 || index === 2 ? <Icon name="arrowUp" /> : null} -->
    <!-- {count} -->
  </div>
  <p class="mx-auto text-sm text-neutral-600 dark:text-neutral-400 sm:text-base">
    {description}
  </p>
</li>
