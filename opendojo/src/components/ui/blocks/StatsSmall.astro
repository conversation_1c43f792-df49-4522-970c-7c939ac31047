---
// Extract the properties from Astro.props
const { title, subTitle } = Astro.props;
// Define TypeScript interface for the properties
interface Props {
  title: string;
  subTitle: string;
}
---

<!-- Container for title and subtitle -->
<div>
  <p class="text-3xl font-bold text-orange-400 dark:text-orange-300">{title}</p>
  <p class="mt-1 text-neutral-600 dark:text-neutral-400">{subTitle}</p>
</div>
