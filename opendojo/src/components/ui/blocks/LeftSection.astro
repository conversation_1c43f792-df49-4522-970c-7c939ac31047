---
// Import the necessary modules
import { Image } from "astro:assets";
import PrimaryCTA from "@components/ui/buttons/PrimaryCTA.astro";
// Destructure the props passed to the Astro component
const { title, subTitle, btnExists, btnTitle, btnURL, img, imgAlt } =
  Astro.props;
// Define TypeScript interface for props
interface Props {
  title: string;
  subTitle: string;
  btnExists?: boolean;
  btnTitle?: string;
  btnURL?: string;
  img: any;
  imgAlt: any;
}
---

<!-- The root section of the component -->
<section
  class="mx-auto max-w-[85rem] items-center gap-8 px-4 py-10 sm:px-6 sm:py-16 md:grid md:grid-cols-2 lg:grid lg:grid-cols-2 lg:px-8 lg:py-14 xl:gap-16 2xl:max-w-full"
>
  <!-- The Image component which renders the image -->
  <Image
    class="w-full rounded-xl"
    src={img}
    alt={imgAlt}
    draggable={"false"}
    format={"avif"}
  />
  <!-- The container for title, subtitle, and optional CTA button -->
  <div class="mt-4 md:mt-0">
    <!-- The title of the section -->
    <h2
      class="mb-4 text-balance text-4xl font-extrabold tracking-tight text-neutral-800 dark:text-neutral-200"
    >
      {title}
    </h2>
    <!-- The subtitle of the section -->
    <p
      class="mb-4 max-w-prose text-pretty font-normal text-neutral-600 dark:text-neutral-400 sm:text-lg"
    >
      {subTitle}
    </p>
    <!-- Conditionally render the Primary CTA button if btnExists is true -->
    {btnExists ? <PrimaryCTA title={btnTitle} url={btnURL} /> : null}
  </div>
</section>
