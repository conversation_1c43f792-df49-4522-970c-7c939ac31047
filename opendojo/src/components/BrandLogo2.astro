<svg xmlns="http://www.w3.org/2000/svg" {...Astro.props} viewBox="0 0 240 240" width="240" height="240" fill="none">
  <!-- Background Circle -->
  <circle cx="120" cy="120" r="115" fill="#1A202C" />

  <!-- Outer Ring -->
  <circle cx="120" cy="120" r="105" fill="none" stroke="#4FD1C5" stroke-width="4" />

  <!-- Flatter Diamond (reduced vertical height) -->
  <!-- <path d="M120 45 L190 120 L120 195 L50 120 Z" fill="none" stroke="#4FD1C5" stroke-width="5" stroke-linejoin="round" /> -->

  <path d="M120 35 L200 120 L120 205 L40 120 Z" fill="none" stroke="#4FD1C5" stroke-width="5" stroke-linejoin="round" />

  <!-- Corner Dots -->
  <circle cx="120" cy="35" r="8" fill="#4FD1C5" />
  <circle cx="200" cy="120" r="8" fill="#4FD1C5" />
  <circle cx="120" cy="205" r="8" fill="#4FD1C5" />
  <circle cx="40" cy="120" r="8" fill="#4FD1C5" />

  <!-- Text "Open" (inside flatter diamond) -->
  <text x="120" y="110" font-family="Arial, sans-serif" font-size="30" font-weight="bold" fill="white" text-anchor="middle">OPEN</text>

  <!-- Text "Dojo" (inside flatter diamond) -->
  <text x="120" y="145" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white" text-anchor="middle">DOJO</text>
</svg>
