<svg xmlns="http://www.w3.org/2000/svg" {...Astro.props} viewBox="0 0 240 240" width="240" height="240" fill="none">
  <!-- Background Circle -->
  <circle cx="120" cy="120" r="115" fill="#1A202C" />

  <!-- Outer Ring -->
  <circle cx="120" cy="120" r="105" fill="none" stroke="#4FD1C5" stroke-width="4" />

  <!-- Larger Diamond (to contain text) -->
  <!-- <path d="M120 30 L190 120 L120 210 L50 120 Z" fill="none" stroke="#4FD1C5" stroke-width="5" stroke-linejoin="round" /> -->

  <path d="M120 45 L190 120 L120 195 L50 120 Z" fill="none" stroke="#4FD1C5" stroke-width="5" stroke-linejoin="round" />

  <!-- Corner Dots -->
  <circle cx="120" cy="30" r="8" fill="#4FD1C5" />
  <circle cx="190" cy="120" r="8" fill="#4FD1C5" />
  <circle cx="120" cy="210" r="8" fill="#4FD1C5" />
  <circle cx="50" cy="120" r="8" fill="#4FD1C5" />

  <!-- Text "Open" (inside diamond) -->
  <text x="120" y="110" font-family="Arial, sans-serif" font-size="30" font-weight="bold" fill="white" text-anchor="middle">OPEN</text>

  <!-- Text "Dojo" (inside diamond) -->
  <text x="120" y="145" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white" text-anchor="middle">DOJO</text>
</svg>
