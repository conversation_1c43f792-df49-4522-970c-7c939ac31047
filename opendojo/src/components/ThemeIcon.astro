<!-- Dark Theme Toggle Button --><!-- This button is shown when the light theme is active, and when clicked, it switches the theme to dark -->
<button
  type="button"
  aria-label="Dark Theme Toggle"
  class="hs-dark-mode group flex h-8 w-8 items-center justify-center rounded-full font-medium text-neutral-600 outline-hidden ring-zinc-500 transition duration-300 hover:bg-neutral-200 hover:text-orange-400 hs-dark-mode-active:hidden dark:text-neutral-400 dark:ring-zinc-200 dark:hover:text-orange-300 dark:focus:outline-hidden"
  data-hs-theme-click-value="dark"
>
  <!-- The SVG displayed shows an abstract icon that represents the moon (dark theme) -->
  <svg
    class="h-4 w-4 shrink-0"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    ><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg
  >
  <!-- Light Theme Toggle Button -->
  <!-- This button is hidden by default and only appears when the dark theme is active, when clicked, it switches to the light theme -->
</button>
<button
  type="button"
  aria-label="Light Theme Toggle"
  class="hs-dark-mode group hidden h-8 w-8 items-center justify-center rounded-full font-medium text-neutral-600 outline-hidden ring-zinc-500 transition duration-300 hover:text-orange-400 hs-dark-mode-active:flex dark:text-neutral-400 dark:ring-zinc-200 dark:hover:bg-neutral-700 dark:hover:text-orange-300 dark:focus:outline-hidden"
  data-hs-theme-click-value="light"
>
  <!-- The SVG displayed shows a standard sun icon that stands for the light theme -->
  <svg
    class="h-4 w-4 shrink-0"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    stroke-width="2"
    stroke-linecap="round"
    stroke-linejoin="round"
    ><circle cx="12" cy="12" r="4"></circle><path d="M12 8a2 2 0 1 0 4 4"
    ></path><path d="M12 2v2"></path><path d="M12 20v2"></path><path
      d="m4.93 4.93 1.41 1.41"></path><path d="m17.66 17.66 1.41 1.41"
    ></path><path d="M2 12h2"></path><path d="M20 12h2"></path><path
      d="m6.34 17.66-1.41 1.41"></path><path d="m19.07 4.93-1.41 1.41"
    ></path></svg
  >
</button>
