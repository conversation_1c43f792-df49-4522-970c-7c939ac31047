@import 'tailwindcss';

@layer base {
    button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}
/* Dark mode colors. */
:root {
    --primary-button-hover: #ff801f;
    --backdrop-color: #272727cc;
    --sl-color-accent: #ff801f;
    --sl-color-accent-high: #ffa057;
    --sl-color-accent-low: #562800;
    --sl-color-black: #181818;
    --sl-color-gray-1: #eee;
    --sl-color-gray-2: #c2c2c2;
    --sl-color-gray-3: #8b8b8b;
    --sl-color-gray-4: #585858;
    --sl-color-gray-5: #383838;
    --sl-color-gray-6: #272727;
    --sl-color-white: #fff;
    --yellow-hsl: 43.3, 96.4%, 56.3%;
    --overlay-yellow: hsla(var(--yellow-hsl), .2);
    --border: hsla(var(--border-neutral), .4);
    --border-neutral: 0, 0%, 25.1%;
}

/* Light mode colors. */
:root[data-theme='light'] {
    --primary-button-hover: #ff801f;
    --backdrop-color: #f6f6f699;
    --sl-color-accent: #f76b15;
    --sl-color-accent-high: #562800;
    --sl-color-accent-low: #ffa057;
    --sl-color-black: #fff;
    --sl-color-gray-1: #272727;
    --sl-color-gray-2: #383838;
    --sl-color-gray-3: #585858;
    --sl-color-gray-4: #8b8b8b;
    --sl-color-gray-5: #c2c2c2;
    --sl-color-gray-6: #eee;
    --sl-color-gray-7: #f6f6f6;
    --sl-color-white: #181818;
    --yellow-hsl: 47.9, 95.8%, 53.1%;
    --border-yellow: 54.9, 96.7%, 88%;
    --border: hsla(var(--border-yellow), .4);
}

.page {
    background: linear-gradient(215deg, var(--overlay-yellow), transparent 40%), radial-gradient(var(--overlay-yellow), transparent 40%) no-repeat center center / cover, radial-gradient(var(--overlay-yellow), transparent 65%) no-repeat center center / cover;
    background-blend-mode: overlay;
}

header {
    border: none !important;
    padding: 0 !important;
}

header.header {
    background-color: transparent !important;
    height: 4.5rem !important;
    margin-inline: auto !important;
    padding-block: 0 !important;
    padding-inline: 2rem !important;
}

header > div:first-of-type {
    backdrop-filter: blur(12px) !important;
    background-color: var(--backdrop-color) !important;
    border: 1px var(--border) solid;
    border-radius: 36px;
    height: 100% !important;
    margin-inline: auto !important;
    margin-top: 1rem !important;
    max-width: 1536px;
    padding-inline: 2rem !important;
    width: auto !important;
}

select {
    background-image: none;
    box-shadow: none;
}

.sl-link-button.primary:hover {
    background-color: var(--primary-button-hover);
    transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.sl-link-button.primary:hover svg {
    transform: translateX(0.25rem);
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
}