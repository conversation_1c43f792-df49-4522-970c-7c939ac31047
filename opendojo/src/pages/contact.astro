---
// Import the necessary components
import MainLayout from "@/layouts/MainLayout.astro";
import ContactSection from "@components/sections/misc/ContactSection.astro";
import { SITE } from "@data/constants";

const pageTitle: string = `Contact | ${SITE.title}`;
---

<!--Utilizing MainLayout for the outer layout of the page, and defining meta for SEO purposes-->
<MainLayout
  title={pageTitle}
  structuredData={{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": "https://opendojo.com/contact",
    "url": "https://opendojo.com/contact",
    "name": "Contact Us | ScrewFast",
    "description":
      "Have questions or want to discuss a project? Reach out, and let's craft the perfect solution with our tools and services.",
    "isPartOf": {
      "@type": "WebSite",
      "url": "https://opendojo.com",
      "name": "ScrewFast",
      "description":
        "ScrewFast offers top-tier hardware tools and expert construction services to meet all your project needs.",
    },
    "inLanguage": "en-US"
  }}
>
  <ContactSection />
</MainLayout>
