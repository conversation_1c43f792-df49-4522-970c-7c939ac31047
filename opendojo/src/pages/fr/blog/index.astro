---
// Import necessary components, modules and types
import MainLayout from "@/layouts/MainLayout.astro";
import CardBlog from "@components/ui/cards/CardBlog.astro";
import CardBlogRecent from "@components/ui/cards/CardBlogRecent.astro";
import CardInsight from "@components/ui/cards/CardInsight.astro";
import { getCollection } from "astro:content";
import type { CollectionEntry } from "astro:content";
import { SITE } from "@data/constants";

// Get all blogs post in French and sort them based on publish date

const frenchBlogEntries = await getCollection("blog", ({ id }) => {
  return id.startsWith("fr/");
});

const frenchInsightsEntries = await getCollection("insights", ({ id }) => {
  return id.startsWith("fr/");
});

const blogPosts: CollectionEntry<"blog">[] = frenchBlogEntries.sort(
  (a: CollectionEntry<"blog">, b: CollectionEntry<"blog">) =>
    b.data.pubDate.valueOf() - a.data.pubDate.valueOf(),
);
// Get all insights posts
const insightPosts: CollectionEntry<"insights">[] = frenchInsightsEntries;

// Separate the most recent post from others
const mostRecentPost: CollectionEntry<"blog"> = blogPosts[0];
const otherPosts: CollectionEntry<"blog">[] = blogPosts.slice(1);

// Define variables for page content
const title: string = "Votre Passerelle vers l'Excellence en Construction";
const subTitle: string =
  "Explorez les dernières actualités, astuces et analyses de ScrewFast pour améliorer vos projets de construction. Des mises en avant de produits aux stratégies de gestion de projet, notre blog est votre ressource incontournable pour tout ce qui concerne les outils et la construction.";
const secondTitle: string = "Perspectives";
const secondSubTitle: string =
  "Restez à jour avec les dernières tendances et évolutions de l'industrie de la construction grâce aux analyses de l'équipe d'experts de ScrewFast.";

const pageTitle: string = `Blog | ${SITE.title}`;
---

<MainLayout lang="fr"
  title={pageTitle}
  structuredData={{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": "https://opendojo.com/fr/blog",
    "url": "https://opendojo.com/fr/blog",
    "name": "Blog | ScrewFast",
    "description": "Restez informé des dernières tendances et évolutions dans le secteur de la construction avec les analyses de l'équipe d'experts de ScrewFast.",
    "isPartOf": {
      "@type": "WebSite",
      "url": "https://opendojo.com/fr",
      "name": "ScrewFast",
      "description": "ScrewFast propose des outils matériels de premier ordre et des services de construction experts pour répondre à tous vos besoins de projet."
    },
    "inLanguage": "fr"
  }}
>
  <section
    class="mx-auto max-w-[85rem] space-y-8 px-4 pt-16 sm:px-6 lg:px-8 2xl:max-w-full"
  >
    <!--Page header-->
    <div class="mx-auto max-w-3xl text-left sm:text-center">
      <h1
        class="block text-balance text-4xl font-bold tracking-tight text-neutral-800 dark:text-neutral-200 md:text-5xl lg:text-6xl"
      >
        {title}
      </h1>

      <p
        class="mt-4 text-pretty text-lg text-neutral-600 dark:text-neutral-400"
      >
        {subTitle}
      </p>
    </div>
  </section>

  <section
    class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
  >
    <!--Blog posts grid-->
    <div class="grid gap-6 lg:grid-cols-2">
      {otherPosts.map((blogEntry) => <CardBlog blogEntry={blogEntry} blogLocale="fr"/>)}
    </div>
  </section>
  <!--Most recent blog post-->
  <section
    class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
  >
    <CardBlogRecent blogEntry={mostRecentPost} recentBlogLocale="fr" />
  </section>
  <!--Insights section-->
  <section
    class="mx-auto max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 2xl:max-w-full"
  >
    <div class="mx-auto mb-10 max-w-2xl text-center lg:mb-14">
      <h2
        class="text-2xl font-bold text-neutral-800 dark:text-neutral-200 md:text-4xl md:leading-tight"
      >
        {secondTitle}
      </h2>
      <p class="mt-1 text-pretty text-neutral-600 dark:text-neutral-400">
        {secondSubTitle}
      </p>
    </div>
    <div class="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
      {
        insightPosts.map((insightEntry) => (
          <CardInsight insightEntry={insightEntry} insightLocale="fr" />
        ))
      }
    </div>
  </section>
</MainLayout>
