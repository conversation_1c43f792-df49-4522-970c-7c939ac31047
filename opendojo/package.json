{"name": "opendojo", "type": "module", "version": "0.0.2", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build && node process-html.mjs", "dist": "npm run build && tar -czvf opendojo-v$npm_package_version.tar.gz ./dist", "preview": "astro preview", "astro": "astro", "npmcheck": "npx npm-check", "depcheck": "npx depcheck"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^4.3.0", "@astrojs/sitemap": "^3.4.1", "@astrojs/starlight": "^0.35.1", "@tailwindcss/vite": "^4.1.11", "astro": "^5.12.0", "astro-compressor": "^1.0.0", "clipboard": "^2.0.11", "globby": "^14.1.0", "gsap": "^3.13.0", "html-minifier-terser": "^7.2.0", "lenis": "^1.3.8", "preline": "^3.1.0", "sharp": "^0.34.3", "sharp-ico": "^0.1.5", "tailwindcss": "^4.1.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "astro-vtbot": "^2.1.6", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "^5.8.3", "wrangler": "^4.24.3"}}