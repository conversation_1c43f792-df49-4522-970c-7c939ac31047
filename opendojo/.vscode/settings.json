{
  // Enable pasting files into a Markdown editor to create Markdown links.
  "markdown.editor.filePaste.enabled": true,

  // <PERSON><PERSON> pasted media files into a Markdown editor to the workspace.
  "markdown.editor.filePaste.copyIntoWorkspace": "mediaFiles",

  // Enable dropping files into a Markdown editor to create Markdown links.
  "markdown.editor.drop.enabled": true,

  // <PERSON><PERSON> dropped media files into a Markdown editor to the workspace.
  "markdown.editor.drop.copyIntoWorkspace": "mediaFiles",

  // Define the destination folder for copied files.
  "markdown.copyFiles.destination": {
    "/src/content/**/*": "/src/images/content/${documentBaseName}/"
  },
}
