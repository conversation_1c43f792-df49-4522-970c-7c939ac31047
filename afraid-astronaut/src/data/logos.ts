import type { ImageMetadata } from 'astro';

// Import all logo images
import logo1 from '../assets/images/logos/logoipsum-333.svg';
import logo2 from '../assets/images/logos/logoipsum-335.svg';
import logo3 from '../assets/images/logos/logoipsum-344.svg';
import logo4 from '../assets/images/logos/logoipsum-347.svg';
import logo5 from '../assets/images/logos/logoipsum-352.svg';
import logo6 from '../assets/images/logos/logoipsum-356.svg';
import logo7 from '../assets/images/logos/logoipsum-358.svg';
import logo8 from '../assets/images/logos/logoipsum-360.svg';

export interface Logo {
    src: ImageMetadata;
    alt: string;
}

export interface LogoList {
    id: string;
    title: string;
    logos: Logo[];
}

export const logoLists = {
    partners: {
        id: 'partners',
        title: 'Trusted by innovative companies',
        logos: [
            {
                src: logo6,
                alt: 'Company 1',
                width: 150,
                height: 50
            },
            {
                src: logo7,
                alt: 'Company 2',
                width: 150,
                height: 50
            },
            {
                src: logo8,
                alt: 'Company 3',
                width: 150,
                height: 50
            },
        ]
    },
    main: {
        id: 'main',
        title: 'Our Partners',
        logos: [
            {
                src: logo1,
                alt: 'Partner 1',
                width: 150,
                height: 50
            },
            {
                src: logo2,
                alt: 'Partner 2',
                width: 150,
                height: 50
            },
            {
                src: logo3,
                alt: 'Partner 3',
                width: 150,
                height: 50
            },
            {
                src: logo4,
                alt: 'Partner 4',
                width: 150,
                height: 50
            },
            {
                src: logo5,
                alt: 'Partner 5',
                width: 150,
                height: 50
            },
        ]
    }
    // Other logo lists...
};
