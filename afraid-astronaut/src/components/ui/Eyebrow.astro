---
import { getEyebrowColor } from '@utils/styleUtils';
import type { ThemeColor } from '@utils/styleUtils';
import Animate from '@components/ui/Animate.astro';

export interface Props {
    text: string;
    background?: ThemeColor;
    className?: string;
    animation?: 'fade-up' | 'fade-down' | 'fade-left' | 'fade-right' | 'fade-in' | 'scale-up';
    delay?: number;
}

const {
    text,
    background = 'base',
    className = '',
    animation = 'fade-up',
    delay = 0
} = Astro.props;

const eyebrowColor = getEyebrowColor(background);
---
<Animate animation={animation} delay={delay}>
    <div
        class:list={[
            eyebrowColor,
            "font-medium mb-4 uppercase tracking-wider text-sm",
            className
        ]}
    >
        {text}
    </div>
</Animate>
