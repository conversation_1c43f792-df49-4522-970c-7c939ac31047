---
import { getTextColor, getHeadlineColor, getBackgroundColor } from '../../../utils/styleUtils';
import type { ThemeColor } from '../../../utils/styleUtils';
import { getInputBackgroundColor, getInputTextColor } from '../../../utils/styleUtils';

interface Props {
    label: string;
    id: string;
    name: string;
    rows?: number;
    placeholder?: string;
    required?: boolean;
    background?: ThemeColor;
    class?: string;
}

const { 
    label, 
    id, 
    name, 
    rows = 4, 
    placeholder = '', 
    required = false,
    background = 'base',
    class: className = ''
} = Astro.props;

const labelClass = getHeadlineColor(background);
const textClass = getTextColor(background);
const bgClass = getBackgroundColor(background === 'dark' ? 'light' : 'base');
const inputBgClass = getInputBackgroundColor(background);
const inputTextClass = getInputTextColor(background);
---
<div class="form-group">
    <label 
        for={id} 
        class={`block text-sm font-medium mb-1 ${labelClass}`}
    >
        {label}
        {required && <span class="text-red-500">*</span>}
    </label>
    <textarea
        id={id}
        name={name}
        rows={rows}
        placeholder={placeholder}
        required={required}
        class={`
            w-full px-4 py-2 
            ${inputBgClass}
            ${inputTextClass}
            border border-primary/20
            rounded-lg 
            focus:ring-2 focus:ring-primary focus:border-primary 
            placeholder:text-body-base/50
            ${className}
        `}
    ></textarea>
</div> 