---
import { getTextColor, getHeadlineColor, getBackgroundColor, getInputBackgroundColor, getInputTextColor } from '../../../utils/styleUtils';
import type { ThemeColor } from '../../../utils/styleUtils';

interface Option {
    label: string;
    value: string;
}

interface Props {
    label: string;
    id: string;
    name: string;
    options: Option[];
    required?: boolean;
    placeholder?: string;
    background?: ThemeColor;
    class?: string;
}

const { 
    label, 
    id,
    name, 
    options,
    required = false,
    placeholder = 'Select an option',
    background = 'base',
    class: className = ''
} = Astro.props;

const labelClass = getHeadlineColor(background);
const textClass = getTextColor(background);
const bgClass = getBackgroundColor(background === 'dark' ? 'light' : 'base');
const inputBgClass = getInputBackgroundColor(background);
const inputTextClass = getInputTextColor(background);
---
<div class="form-group">
    <label 
        for={id} 
        class={`block text-sm font-medium mb-1 ${labelClass}`}
    >
        {label}
        {required && <span class="text-red-500">*</span>}
    </label>
    <select
        id={id}
        name={name}
        required={required}
        class={`
            w-full px-3 py-2 
            ${inputBgClass}
            ${inputTextClass}
            border border-primary/20
            rounded-lg 
            focus:ring-2 focus:ring-primary focus:border-primary 
            ${className}
        `}
    >
        <option value="" disabled selected={!required}>
            {placeholder}
        </option>
        {options.map((option) => (
            <option value={option.value}>
                {option.label}
            </option>
        ))}
    </select>
</div> 