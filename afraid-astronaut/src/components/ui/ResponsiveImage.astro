---
import { Image } from 'astro:assets';
import type { ImageMetadata } from 'astro';

interface Props {
  src: ImageMetadata;
  alt: string;
  class?: string;
  widths?: number[];
  sizes?: string;
  loading?: 'lazy' | 'eager';
  format?: 'webp' | 'avif' | 'png' | 'jpg';
  quality?: number;
  aspectRatio?: number;
}

const {
  src,
  alt,
  class: className = '',
  widths = [400, 800, 1200, 1600],
  sizes = '(max-width: 768px) 100vw, 50vw',
  loading = 'lazy',
  format = 'webp',
  quality = 80,
  aspectRatio
} = Astro.props;
---

<div class={`overflow-hidden ${className}`} style={aspectRatio ? `aspect-ratio: ${aspectRatio}` : ''}>
  <Image
    src={src}
    alt={alt}
    widths={widths}
    sizes={sizes}
    loading={loading}
    format={format}
    quality={quality}
    class="w-full h-full object-cover"
  />
</div>