---
// A lightweight animation component using native IntersectionObserver
interface Props {
  animation: 'fade-up' | 'fade-down' | 'fade-left' | 'fade-right' | 'fade-in' | 'scale-up';
  delay?: number;
  duration?: number;
  threshold?: number;
  once?: boolean;
  class?: string;
}

const { 
  animation = 'fade-up',
  delay = 0,
  duration = 600,
  threshold = 0.1,
  once = true,
  class: className = ''
} = Astro.props;
---

<div 
  class:list={["animate-wrapper", className]} 
  data-animation={animation} 
  data-delay={delay} 
  data-duration={duration}
  data-once={once}
  style={`--animation-duration: ${duration}ms; --animation-delay: ${delay}ms;`}
>
  <slot />
</div>

<script>
  // Only initialize once per page
  if (!window.animateObserverInitialized) {
    window.animateObserverInitialized = true;
    
    const observerCallback = (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target;
          const animation = element.dataset.animation;
          const delay = parseInt(element.dataset.delay || '0');
          
          // Add animation class after specified delay
          setTimeout(() => {
            element.classList.add('animated', animation);
          }, delay);
          
          // Unobserve if once is true
          if (element.dataset.once !== 'false') {
            observer.unobserve(element);
          }
        }
      });
    };
    
    // Create observer
    const observer = new IntersectionObserver(observerCallback, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });
    
    // Observe all elements
    document.addEventListener('astro:page-load', () => {
      document.querySelectorAll('.animate-wrapper').forEach(element => {
        observer.observe(element);
      });
    });
    
    // Initial observation on first load
    document.querySelectorAll('.animate-wrapper').forEach(element => {
      observer.observe(element);
    });
  }
</script>

<style>
  .animate-wrapper {
    opacity: 0;
    transition: opacity var(--animation-duration) ease, transform var(--animation-duration) ease;
    will-change: opacity, transform;
  }
  
  .animated {
    opacity: 1;
  }
  
  .fade-up.animated {
    transform: translateY(0);
  }
  
  .fade-up {
    transform: translateY(30px);
  }
  
  .fade-down.animated {
    transform: translateY(0);
  }
  
  .fade-down {
    transform: translateY(-30px);
  }
  
  .fade-left.animated {
    transform: translateX(0);
  }
  
  .fade-left {
    transform: translateX(30px);
  }
  
  .fade-right.animated {
    transform: translateX(0);
  }
  
  .fade-right {
    transform: translateX(-30px);
  }
  
  .fade-in {
    transform: none;
  }
  
  .scale-up {
    transform: scale(0.95);
  }
  
  .scale-up.animated {
    transform: scale(1);
  }
</style>