---
import { Gith<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-astro'
import X from '@components/icons/X.astro';
import { Image } from 'astro:assets';
interface Props {
entry: any; // You might want to type this properly with your collection type
}

const { entry } = Astro.props;
---
<div class="lg:hidden bg-white border border-black p-4 sm:p-6 rounded-md">
<div class="flex gap-6 sm:gap-8 items-center h-48 sm:h-56">
    <div class="flex flex-col justify-between min-w-0 flex-1 h-full">
    <div>
        <h2 class="text-h5 font-bold text-body-base mb-1">{entry.data.name}</h2>
        <p class="text-small text-body-base mb-3">{entry.data.jobTitle}</p>
        
        {entry.data.email && (
        <a 
            href={`mailto:${entry.data.email}`}  
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small truncate"
        >
            <Mail size={18} strokeWidth={1.25} />
            <span class="truncate">{entry.data.email}</span>
        </a>
        )}
    </div>

    <div class="flex flex-col gap-2 mt-3">
        {entry.data.github && (
        <a 
            href={entry.data.github} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="GitHub Profile"
        >
            <Github size={18} strokeWidth={1.25} />
            {entry.data.githubUsername && <span>{entry.data.githubUsername}</span>}
        </a>
        )}
        {entry.data.xSocial && (
        <a 
            href={entry.data.xSocial} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="X (Twitter) Profile"
        >
            <X size={16} />
            {entry.data.xSocialUsername && <span>@{entry.data.xSocialUsername}</span>}
        </a>
        )}
        {entry.data.linkedin && (
        <a 
            href={entry.data.linkedin} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="LinkedIn Profile"
        >
            <Linkedin size={18} strokeWidth={1.25} />
            {entry.data.linkedinUsername && <span>{entry.data.linkedinUsername}</span>}
        </a>
        )}
    </div>
    </div>

    <div class="h-full aspect-[3/4] shrink-0">
    <Image
        class="h-full w-full rounded-md object-cover border border-black"
        src={entry.data.headshot}
        alt={entry.data.name}
        width={896}
        height={1280}
        quality={80}
        format="webp"
    />
    </div>
</div>
</div>
