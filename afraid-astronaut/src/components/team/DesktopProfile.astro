---
import { Gith<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-astro'
import X from '@components/icons/X.astro';
import { Image } from 'astro:assets';


interface Props {
  entry: any; // You might want to type this properly with your collection type
}

const { entry } = Astro.props;
---

<div class="hidden lg:block">
<Image
    class="w-full rounded-md object-cover mb-6 border border-black"
    src={entry.data.headshot}
    alt={entry.data.name}
    width={896}
    height={1280}
    quality={80}
    format="webp"
/>
<div class="flex flex-col items-center">
    <h2 class="text-h5 font-bold text-body-base mb-2">{entry.data.name}</h2>
    <p class="text-small text-body-base mb-4">{entry.data.jobTitle}</p>
    
    <div class="flex flex-col items-center gap-4">
    {entry.data.email && (
        <a 
        href={`mailto:${entry.data.email}`} 
        class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
        >
        <Mail size={18} strokeWidth={1.25} />
        <span class="break-all">{entry.data.email}</span>
        </a>
    )}
    
    <div class="flex flex-col items-center gap-3">
        {entry.data.github && (
        <a 
            href={entry.data.github} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="GitHub Profile"
        >
            <Github size={18} strokeWidth={1.25} />
            {entry.data.githubUsername && <span>{entry.data.githubUsername}</span>}
        </a>
        )}
        {entry.data.xSocial && (
        <a 
            href={entry.data.xSocial} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="X (Twitter) Profile"
        >
            <X size={16} />
            {entry.data.xSocialUsername && <span>@{entry.data.xSocialUsername}</span>}
        </a>
        )}
        {entry.data.linkedin && (
        <a 
            href={entry.data.linkedin} 
            target="_blank" 
            rel="noopener noreferrer"
            class="flex items-center gap-2 text-body-base hover:text-primary transition-colors text-small"
            aria-label="LinkedIn Profile"
        >
            <Linkedin size={18} strokeWidth={1.25} />
            {entry.data.linkedinUsername && <span>{entry.data.linkedinUsername}</span>}
        </a>
        )}
    </div>
    </div>
</div>
</div>
