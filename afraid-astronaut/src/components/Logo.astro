---
import { Image } from 'astro:assets';
import logo from '../assets/astro.svg';
interface Props {
    variant?: 'header' | 'footer' | 'dark';
}

const { variant = 'header' } = Astro.props;

// Determine the appropriate CSS class based on the variant
const getLogoClass = () => {
    switch(variant) {
        case 'header':
            return 'text-header-logo hover:text-primary';
        case 'footer':
            return 'text-footer-logo hover:text-primary';
        case 'dark':
            return 'text-body-base hover:text-primary';
        default:
            return 'text-body-base hover:text-primary';
    }
};
---
<a href="/" class:list={[
    'logo uppercase font-logo transition-colors duration-200 font-light',
    getLogoClass()
]}>
    <Image
        src={logo}
        alt="Logo"
        width={150}
        height={40}
        format="svg"
        class="hidden"
    />
    <span class="font-bold">Titan</span> Core
</a>
