---
import Button from "@components/ui/Button.astro";
import Input from "@components/ui/form/Input.astro";
import Textarea from "@components/ui/form/Textarea.astro";
import Radio from "@components/ui/form/Radio.astro";
import Checkbox from "@components/ui/form/Checkbox.astro";
import Select from "@components/ui/form/Select.astro";

const hearAboutOptions = [
    { label: "Search Engine", value: "search" },
    { label: "Social Media", value: "social" },
    { label: "Friend/Colleague", value: "referral" },
    { label: "Other", value: "other" }
];

const interestOptions = [
    { label: "Web Development", value: "web" },
    { label: "Mobile Development", value: "mobile" },
    { label: "Design Services", value: "design" },
    { label: "Consulting", value: "consulting" }
];

const serviceOptions = [
    { label: "Website Development", value: "website" },
    { label: "Mobile App Development", value: "mobile" },
    { label: "UI/UX Design", value: "design" },
    { label: "Digital Marketing", value: "marketing" },
    { label: "Consulting", value: "consulting" }
];

const budgetOptions = [
    { label: "$5,000 - $10,000", value: "5-10k" },
    { label: "$10,000 - $25,000", value: "10-25k" },
    { label: "$25,000 - $50,000", value: "25-50k" },
    { label: "$50,000+", value: "50k+" }
];

const formBackground = 'light';
---
<form class="space-y-6 bg-white p-6 rounded-lg">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
        label="Name"
        id="name"
        name="name"
        required
        background={formBackground}
        />
        <Input
        label="Email"
        type="email"
        id="email"
        name="email"
        required
        background={formBackground}
        />
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <Input
        label="Phone"
        type="tel"
        id="phone"
        name="phone"
        background={formBackground}
    />
    <Input
        label="Company"
        id="company"
        name="company"
        background={formBackground}
    />
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Radio
            label="How did you hear about us?"
            id="hear-about"
            name="hear-about"
            options={hearAboutOptions}
            required
            background={formBackground}
        />
    <Checkbox
        label="Areas of Interest"
        id="interests"
        name="interests"
        options={interestOptions}
        background={formBackground}
    />
    </div>
    
    <Textarea
        label="Message"
        id="message"
        name="message"
        required
        background={formBackground}
    />
    
    <Checkbox
        label="I agree to the terms and conditions"
        id="terms"
        name="terms"
        required
        background={formBackground}
    />
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Select
            label="What service are you interested in?"
            id="service"
            name="service"
            options={serviceOptions}
            required
            placeholder="Choose a service"
            background={formBackground}
        />
        <Select
            label="Budget Range"
            id="budget"
            name="budget"
            options={budgetOptions}
            required
            placeholder="Select your budget"
            background={formBackground}
        />
    </div>



    <div class="mt-6">
        <Button>Submit</Button>
    </div>
</form>
