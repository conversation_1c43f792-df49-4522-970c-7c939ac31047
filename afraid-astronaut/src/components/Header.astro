---
import { headerMenu } from '@data/menu';
import Logo from '@components/Logo.astro';
import { Menu, X, ChevronDown } from 'lucide-astro';
import Button from '@components/ui/Button.astro';

const ctaButton = {
    name: 'Contact Us',
    link: '/contact',
}

// Get the current path to determine active menu item
const currentPath = Astro.url.pathname;

// Function to check if a link is the current page or a parent of the current page
const isCurrentPage = (link: string) => {
    if (link === '/') {
        return currentPath === '/';
    }
    return currentPath.startsWith(link);
};

---

<header class="fixed top-0 z-50 w-full left-0">
    <div class="site-container mx-auto px-4 mt-4">
        <div class="flex justify-between items-center bg-white py-4 rounded-lg px-4 border-black border">
        <Logo />
        <nav class="relative flex items-center gap-2 lg:gap-8" aria-label="Site Navigation">
            <!-- Desktop Menu -->
        <ul class="hidden lg:flex lg:gap-8 items-center">
            {headerMenu.map((item, index) => (
                <li class="relative group">
                    <div class="flex items-center gap-1">
                        <a 
                            href={item.link} 
                            class:list={[
                                "text-small font-medium rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-nav-text-active",
                                isCurrentPage(item.link) 
                                    ? "text-nav-text-current font-semibold" 
                                    : "text-nav-text hover:text-nav-text-hover"
                            ]}
                            aria-expanded={item.children ? "false" : undefined}
                            aria-haspopup={item.children ? "true" : undefined}
                            aria-current={isCurrentPage(item.link) ? "page" : undefined}
                        >
                            {item.name}
                            {item.children && item.showArrow && (
                                <ChevronDown 
                                    size={16} 
                                    class="transform transition-transform inline-block ml-1" 
                                    aria-hidden="true"
                                />
                            )}
                        </a>
                    </div>
                    {item.children && (
                        <ul
                            class:list={[
                                "submenu absolute mt-2 bg-nav-submenu-bg border-black border rounded-md min-w-[200px] transition-all duration-200 opacity-0 invisible overflow-hidden z-50",
                                index === headerMenu.length - 1 ? "right-0" : "left-0"
                            ]}
                            role="menu"
                        >
                            {item.children.map(child => (
                                <li role="none">
                                    <a 
                                        href={child.link}
                                        class:list={[
                                            "block px-4 text-small py-2 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-nav-text-active",
                                            isCurrentPage(child.link) 
                                                ? "text-nav-text-current font-semibold" 
                                                : "text-nav-text hover:text-nav-text-hover"
                                        ]}
                                        role="menuitem"
                                        aria-current={isCurrentPage(child.link) ? "page" : undefined}
                                    >
                                        {child.name}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    )}
                </li>
            ))}
        </ul>

        <Button href={ctaButton.link} variant="primary" >{ctaButton.name}</Button>

        <!-- Mobile Menu Button -->
        <button 
            class="mobile-menu-button relative z-30 p-2 border-none cursor-pointer bg-transparent lg:hidden"
            aria-label="Toggle Menu"
            aria-expanded="false"
        >
            <span class="menu-icon block">
                <Menu size={24} class="text-body-base"/>
            </span>
            <span class="close-icon hidden">
                <X size={24} class="text-white"/>
            </span>
        </button>

        <!-- Mobile Menu Panel -->
        <div 
            class="mobile-menu fixed inset-0 z-20 px-8 pt-20 bg-primary lg:hidden hidden opacity-0 scale-95 transform transition-all duration-200 ease-out overflow-y-auto"
        >
            <ul class="flex flex-col gap-4">
                {headerMenu.map(item => (
                    <li>
                        <div class="text-white">
                            <div class="flex items-center justify-between">
                                <a 
                                    href={item.link} 
                                    class:list={[
                                        "py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-nav-mobile-text-active",
                                        isCurrentPage(item.link) 
                                            ? "text-nav-mobile-text-current font-semibold" 
                                            : "text-nav-mobile-text hover:text-nav-mobile-text-hover"
                                    ]}
                                    aria-current={isCurrentPage(item.link) ? "page" : undefined}
                                >
                                    {item.name}
                                </a>
                                {item.children && (
                                    <button 
                                        class:list={[
                                            "mobile-submenu-button p-2 -mr-2 rounded-md focus:outline-none focus:ring-2 focus:ring-nav-mobile-text-active",
                                            isCurrentPage(item.link) 
                                                ? "text-nav-mobile-text-current" 
                                                : "text-nav-mobile-text hover:text-nav-mobile-text-hover"
                                        ]}
                                        aria-label={`Toggle ${item.name} submenu`}
                                        aria-expanded="false"
                                    >
                                        <ChevronDown 
                                            size={16} 
                                            class="transform transition-transform duration-200" 
                                        />
                                    </button>
                                )}
                            </div>
                            {item.children && (
                                <ul
                                    class="mobile-submenu ml-4 mt-2 space-y-2 hidden"
                                    role="menu"
                                >
                                    {item.children.map(child => (
                                        <li role="none">
                                            <a 
                                                href={child.link}
                                                class:list={[
                                                    "block py-1 rounded-md focus:outline-none focus:ring-2 focus:ring-nav-mobile-text-active",
                                                    isCurrentPage(child.link) 
                                                        ? "text-nav-mobile-text-current font-semibold" 
                                                        : "text-nav-mobile-text hover:text-nav-mobile-text-hover"
                                                ]}
                                                role="menuitem"
                                                aria-current={isCurrentPage(child.link) ? "page" : undefined}
                                            >
                                                {child.name}
                                            </a>
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </li>
                ))}
            </ul>
            
            <!-- Updated mobile CTA button styling -->
            <div class="mt-8 pb-8">
                <Button href={ctaButton.link} variant="ghostLight" >{ctaButton.name}
                </Button>
            </div>
        </div>
        </nav>
    </div>
</header>

<script>
    // Desktop Menu
    const desktopMenuItems = document.querySelectorAll('.group');
    
    desktopMenuItems.forEach(item => {
        const link = item.querySelector('a');
        const submenu = item.querySelector('.submenu');
        const submenuLinks = submenu?.querySelectorAll('a');
        
        if (submenu && link) {
            // Handle hover
            item.addEventListener('mouseenter', () => {
                showSubmenu(submenu as HTMLElement, link as HTMLElement);
            });

            item.addEventListener('mouseleave', () => {
                hideSubmenu(submenu as HTMLElement, link as HTMLElement);
            });

            // Handle keyboard navigation
            link.addEventListener('keydown', (e) => {
                if (e.key === 'ArrowDown' && item.children) {
                    e.preventDefault();
                    showSubmenu(submenu as HTMLElement, link as HTMLElement);
                    submenuLinks?.[0]?.focus();
                }
            });

            // Don't show submenu on focus, only on hover or arrow key
            link.addEventListener('focus', () => {
                hideSubmenu(submenu as HTMLElement, link as HTMLElement);
            });

            // Keyboard navigation within submenu
            submenuLinks?.forEach((submenuLink, index) => {
                submenuLink.addEventListener('keydown', (e) => {
                    const lastIndex = submenuLinks.length - 1;

                    switch (e.key) {
                        case 'ArrowDown':
                            e.preventDefault();
                            if (index < lastIndex) {
                                submenuLinks[index + 1].focus();
                            }
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            if (index > 0) {
                                submenuLinks[index - 1].focus();
                            } else {
                                link.focus();
                                hideSubmenu(submenu as HTMLElement, link as HTMLElement);
                            }
                            break;
                        case 'Escape':
                            e.preventDefault();
                            hideSubmenu(submenu as HTMLElement, link as HTMLElement);
                            link.focus();
                            break;
                        case 'Tab':
                            // Allow normal tab behavior but close submenu
                            hideSubmenu(submenu as HTMLElement, link as HTMLElement);
                            break;
                    }
                });
            });

            // Close submenu when focus leaves
            document.addEventListener('focusin', (e) => {
                if (item && !item.contains(e.target as Node)) {
                    hideSubmenu(submenu as HTMLElement, link as HTMLElement);
                }
            });
        }
    });

    // Helper functions
    function showSubmenu(submenu: HTMLElement, link: HTMLElement) {
        submenu.classList.remove('invisible', 'opacity-0');
        submenu.classList.add('visible', 'opacity-100');
        link.setAttribute('aria-expanded', 'true');
    }

    function hideSubmenu(submenu: HTMLElement, link: HTMLElement) {
        submenu.classList.add('invisible', 'opacity-0');
        submenu.classList.remove('visible', 'opacity-100');
        link.setAttribute('aria-expanded', 'false');
    }

    // Simple mobile submenu toggle
    document.querySelectorAll('.mobile-submenu-button').forEach(button => {
        button.addEventListener('click', () => {
            const submenu = button.parentElement?.nextElementSibling as HTMLElement;
            const chevron = button.querySelector('svg');
            
            // Toggle submenu
            if (submenu) {
                submenu.classList.toggle('hidden');
                chevron?.classList.toggle('rotate-180');
                const isExpanded = submenu.classList.contains('hidden') ? 'false' : 'true';
                button.setAttribute('aria-expanded', isExpanded);
            }

        });
    });

    // Mobile menu toggle with scroll lock
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');
    const menuIcon = document.querySelector('.menu-icon');
    const closeIcon = document.querySelector('.close-icon');

    mobileMenuButton?.addEventListener('click', () => {
        const isHidden = mobileMenu?.classList.contains('hidden');
        
        if (isHidden) {
            document.body.style.overflow = 'hidden';
            mobileMenu?.classList.remove('hidden');
            requestAnimationFrame(() => {
                mobileMenu?.classList.remove('opacity-0', 'scale-95');
                mobileMenu?.classList.add('opacity-100', 'scale-100');
            });
            menuIcon?.classList.add('hidden');
            closeIcon?.classList.remove('hidden');
        } else {
            document.body.style.overflow = '';
            mobileMenu?.classList.add('opacity-0', 'scale-95');
            mobileMenu?.classList.remove('opacity-100', 'scale-100');
            setTimeout(() => {
                mobileMenu?.classList.add('hidden');
            }, 200);
            menuIcon?.classList.remove('hidden');
            closeIcon?.classList.add('hidden');
        }
    });
</script>