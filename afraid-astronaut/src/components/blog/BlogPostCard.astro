---
import { Image } from 'astro:assets';
import { formatDate } from '@utils/dateUtils';

const { post } = Astro.props;
const { data, slug } = post;
const { title, description, pubDate, updatedDate, coverImage, category } = data;
---

<article class="bg-background-light rounded-lg overflow-hidden shadow-md transition-all duration-300 hover:shadow-lg">
    {coverImage && (
        <a href={`/blog/${slug}`} class="block overflow-hidden aspect-video">
            <Image
                src={coverImage}
                alt={title}
                width={800}
                height={450}
                class="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                loading="lazy"
                format="webp"
                quality={80}
            />
        </a>
    )}
    <!-- Rest of component -->
</article>