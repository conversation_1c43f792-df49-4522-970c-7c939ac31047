---
const themes = [
    { id: 'zeus', label: '<PERSON>', desc: 'Sky & Thunder' },      // Royal blue & gold
    { id: 'poseidon', label: 'Poseidon', desc: 'Ocean & Seas' },  // Deep blues & teals
    { id: 'hades', label: 'Hades', desc: 'Underworld' },       // Dark purples & grays
    { id: 'apollo', label: 'Apollo', desc: 'Sun & Light' },    // Warm yellows & oranges
    { id: 'artemis', label: '<PERSON>', desc: 'Moon & Hunt' },  // Silver & forest greens
    { id: 'ares', label: 'Ares', desc: 'War & Fire' },        // Reds & blacks
    { id: 'athena', label: '<PERSON>', desc: 'Wisdom' },        // Olive & golds
    { id: 'hermes', label: 'Her<PERSON>', desc: 'Speed' },         // Swift blues & silvers
    { id: 'dionysus', label: '<PERSON>ysus', desc: 'Festivity' }, // Purple & wine reds
    { id: 'demeter', label: 'Demeter', desc: 'Nature' }       // Earth tones & greens
];
---

<div class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-2 z-50" id="theme-switcher">
    <button
        class="flex items-center cursor-pointer justify-center w-10 h-10 rounded-md hover:bg-gray-100"
        id="theme-button"
        aria-label="Change theme"
        aria-haspopup="true"
        aria-expanded="false"
        aria-controls="theme-dropdown"
    >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4.098 19.902a3.75 3.75 0 0 0 5.304 0l6.401-6.402M6.75 21A3.75 3.75 0 0 1 3 17.25V4.125C3 3.504 3.504 3 4.125 3h5.25c.621 0 1.125.504 1.125 1.125v4.072M6.75 21a3.75 3.75 0 0 0 3.75-3.75V8.197M6.75 21h13.125c.621 0 1.125-.504 1.125-1.125v-5.25c0-.621-.504-1.125-1.125-1.125h-4.072M10.5 8.197l2.88-2.88c.438-.439 1.15-.439 1.59 0l3.712 3.713c.44.44.44 1.152 0 1.59l-2.879 2.88M6.75 17.25h.008v.008H6.75v-.008Z" />
        </svg>
    </button>

    <div
        class="absolute bottom-full right-0 mb-2 w-64 bg-white rounded-lg shadow-lg overflow-hidden hidden mr-[-0.5rem]"
        id="theme-dropdown"
        role="menu"
        aria-labelledby="theme-button"
    >
        {themes.map(theme => (
            <button
                data-theme={theme.id}
                class="w-full px-4 py-2 text-left hover:bg-gray-100 flex items-center space-x-3 theme-option cursor-pointer"
                role="menuitem"
                aria-label={`Switch to ${theme.label} theme: ${theme.desc}`}
            >
                <div class="w-6 h-6 rounded-full relative overflow-hidden flex-shrink-0" aria-hidden="true">
                    <div class="absolute top-0 left-0 right-0 h-1/2" style={`background-color: var(--color-primary)`}></div>
                    <div class="absolute bottom-0 left-0 w-1/2 h-1/2" style={`background-color: var(--color-secondary)`}></div>
                    <div class="absolute bottom-0 right-0 w-1/2 h-1/2" style={`background-color: var(--color-accent)`}></div>
                </div>
                <div class="flex flex-col">
                    <span class="text-sm font-medium">{theme.label}</span>
                    <span class="text-xs text-gray-500">{theme.desc}</span>
                </div>
            </button>
        ))}
    </div>
</div>

<script>
    const themeButton = document.getElementById('theme-button');
    const themeDropdown = document.getElementById('theme-dropdown');
    const themeOptions = document.querySelectorAll('.theme-option');
    
    if (!themeButton || !themeDropdown) throw new Error('Theme switcher elements not found');
    
    // Get theme from localStorage or default to 'thor'
    const currentTheme = localStorage.getItem('theme') || 'thor';
    document.body.setAttribute('data-theme', currentTheme);

    // Toggle dropdown
    themeButton.addEventListener('click', () => {
        const isExpanded = themeDropdown.classList.toggle('hidden') === false;
        themeButton.setAttribute('aria-expanded', isExpanded.toString());
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!document.getElementById('theme-switcher')?.contains(e.target as Node )) {
            themeDropdown.classList.add('hidden');
            themeButton.setAttribute('aria-expanded', 'false');
        }
    });

    // Handle theme selection
    themeOptions.forEach(option => {
        option.addEventListener('click', () => {
            const newTheme = (option as HTMLElement).dataset.theme;
            if (newTheme) {
                document.body.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                themeDropdown.classList.add('hidden');
                themeButton.setAttribute('aria-expanded', 'false');
                
                // Announce theme change to screen readers
                const themeName = (option as HTMLElement).querySelector('.text-sm')?.textContent;
                if (themeName) {
                    const announcement = document.createElement('div');
                    announcement.setAttribute('aria-live', 'polite');
                    announcement.classList.add('sr-only');
                    announcement.textContent = `Theme changed to ${themeName}`;
                    document.body.appendChild(announcement);
                    
                    // Remove announcement after it's been read
                    setTimeout(() => {
                        document.body.removeChild(announcement);
                    }, 3000);
                }
            }
        });
    });
</script>

<style>
    .theme-option {
        transition: background-color 0.2s ease;
    }
    
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
    }
</style> 