---
import { getBackgroundColor, getPaddingClass } from '@utils/styleUtils';
import type { PaddingSize, ThemeColor } from '@utils/styleUtils';
import { Image } from 'astro:assets';
import type { LogoList } from '@data/logos';
import Eyebrow from '@components/ui/Eyebrow.astro';

interface Props {
    content: {
        title?: string;
        description?: string;
        eyebrow?: string;
        logos: LogoList;
        headline?: string;
    };
    background?: ThemeColor;
    padding?: PaddingSize;
    paddingTop?: PaddingSize;
    paddingBottom?: PaddingSize;
}

const {
    content: {
        title,
        description,
        eyebrow,
        logos
    },
    background = 'base'
} = Astro.props;

const paddingClass = getPaddingClass({
    padding: Astro.props.padding,
    paddingTop: Astro.props.paddingTop,
    paddingBottom: Astro.props.paddingBottom
});

---

<section class:list={[getBackgroundColor(background), paddingClass]}>
    <div class="site-container px-4">

        <!-- Header content -->
        {eyebrow && (
            <Eyebrow
                text={eyebrow}
                background={background}
                className="text-center"
            />
        )}

        <div class="flex flex-wrap justify-center items-center gap-8 md:gap-12">
            {logos.map((logo) => (
                <div class="flex items-center justify-center">
                    <Image
                        src={logo.src}
                        alt={logo.alt}
                        width={logo.width}
                        height={logo.height}
                        loading="lazy"
                        format="webp"
                        quality={80}
                    />
                </div>
            ))}
        </div>
    </div>
</section>
