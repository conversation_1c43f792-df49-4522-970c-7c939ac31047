---
import Animate from '@components/ui/Animate.astro';
// import ResponsiveImage from '@components/ui/ResponsiveImage.astro';
import Image from 'astro:assets';
import type { ImageMetadata } from 'astro';

interface HeroContent {
    title: string;
    description: string;
    backgroundImage?: ImageMetadata;
    overlayOpacity?: number; // Value between 0 and 1
}

interface Props {
    content: HeroContent;
}

const { content } = Astro.props;
const opacity = content.overlayOpacity ?? 1;
---

<section class="w-full border-b pt-38 pb-18 relative">
    <div class="absolute inset-0 left-0 top-0 w-full h-full overflow-hidden">
            {content.backgroundImage && (
                // <ResponsiveImage
                <Image
                    src={content.backgroundImage}
                    alt="Background image"
                    class="w-full h-full object-cover"
                    quality={70}
                    format="webp"
                    width={1920}
                    height={1080}
                    // widths={[400, 800, 1200, 1600, 2000]}
                    // sizes="100vw"
                />
            )}
            <div
                class="hero-background absolute inset-0 left-0 top-0 w-full h-full overflow-hidden"
                style={`opacity: ${opacity};`}
            ></div>
        </div>

    <div class="site-container mx-auto px-4 relative z-10">
        <div>
            <Animate animation="fade-up">
                <h1 class="text-white">{content.title}</h1>
            </Animate>
            {content.description &&
                <Animate animation="fade-up" delay={100}>
                    <p class="subtitle text-white">{content.description}</p>
                </Animate>
            }
        </div>
    </div>
</section>
