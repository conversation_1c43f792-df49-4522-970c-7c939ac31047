---
import Button from '@components/ui/Button.astro';
import ResponsiveImage from '@components/ui/ResponsiveImage.astro';
import Animate from '@components/ui/Animate.astro';
import type { ImageMetadata } from 'astro';

interface HeroContent {
    title: string;
    description: string;
    buttons?: {
        text: string;
        link: string;
        variant?: 'primary' | 'secondary' | 'ghostLight' | 'ghostDark';
        target?: string;
    }[];
    backgroundImage?: ImageMetadata;
    overlayOpacity?: number;
}

interface Props {
    content: HeroContent;
}

const { content } = Astro.props;
const opacity = content.overlayOpacity ?? 1;
---

<section class="relative min-h-[70vh] flex items-center" transition:animate="slide">
    {content.backgroundImage && (
        <div class="absolute inset-0 z-0">
            <ResponsiveImage
                src={content.backgroundImage}
                alt=""
                class="w-full h-full"
                widths={[400, 800, 1200, 1600, 2000]}
                sizes="100vw"
                loading="eager"
                format="avif"
                quality={80}
            />
            <div class={`absolute inset-0 bg-black opacity-${opacity * 10}`}></div>
        </div>
    )}

    <div class="site-container mx-auto px-4 py-large relative z-10 flex flex-col md:items-center md:justify-center md:text-center">
        <Animate animation="fade-up">
            <h1 class="text-white text-balance">{content.title}</h1>
        </Animate>
        <Animate animation="fade-up" delay={100}>
            <p class="text-white mt-2 text-balance">{content.description}</p>
        </Animate>
        {content.buttons && content.buttons.length > 0 && (
            <Animate animation="fade-up" delay={200}>
                <div class="flex flex-wrap gap-4 mt-8">
                    {content.buttons.map((button) => (
                        <Button
                            href={button.link}
                            target={button.target || '_self'}
                            variant={button.variant || 'primary'}
                        >
                            {button.text}
                        </Button>
                    ))}
                </div>
            </Animate>
        )}
    </div>
</section>
