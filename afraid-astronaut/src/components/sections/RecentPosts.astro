---
import { getCollection } from 'astro:content';
import BlogPost from '@components/blog/BlogPost.astro';
import Button from '@components/ui/Button.astro';
import Eyebrow from '@components/ui/Eyebrow.astro';
import { getPaddingClass, getBackgroundColor, getTextColor } from '@utils/styleUtils';
import type { PaddingSize, ThemeColor } from '@utils/styleUtils';
import Animate from '@components/ui/Animate.astro';

export interface Props {
    content?: {
        eyebrow?: string;
        title?: string;
        description?: string;
        button?: {
            text: string;
            link: string;
            variant?: 'primary' | 'secondary' | 'ghostLight' | 'ghostDark';
        };
    };
    background?: ThemeColor;
    padding?: PaddingSize;
    paddingTop?: PaddingSize;
    paddingBottom?: PaddingSize;
    postsToShow?: number;
    category?: string;
}

const {
    content,
    background = 'base',
    postsToShow = 3,
    category,
} = Astro.props;

// Get background and text colors
const bgColor = getBackgroundColor(background);
const textColor = getTextColor(background);
const paddingClass = getPaddingClass({
    padding: Astro.props.padding,
    paddingTop: Astro.props.paddingTop,
    paddingBottom: Astro.props.paddingBottom
});


// Fetch posts
const allPosts = await getCollection('blog', ({ data }) => {
    return import.meta.env.DEV || data.publish !== false;
});

// Filter and sort posts
let filteredPosts = allPosts
    .filter(post => !category || post.data.categories.includes(category))
    .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
    .slice(0, postsToShow);
---

<section class:list={["relative", bgColor, paddingClass]}>
    <div class="site-container px-4">
        {(content?.title || content?.description) && (
            <div class="max-w-2xl mb-12">
                {content?.eyebrow && (
                    <Eyebrow
                        text={content.eyebrow}
                        background={background}
                    />
                )}
                {content?.title && (
                    <Animate animation="fade-up">
                        <h2 class:list={[textColor]}>
                            {content.title}
                        </h2>
                    </Animate>
                )}
                {content?.description && (
                    <Animate animation="fade-up" delay={100}>
                        <p class:list={["mt-4", textColor, "opacity-90"]}>
                            {content.description}
                        </p>
                    </Animate>
                )}
                {content?.button && (
                    <Animate animation="fade-up" delay={200}>
                        <div class="mt-8">
                            <Button
                                href={content.button.link}
                                variant={content.button.variant || 'primary'}
                            >
                                {content.button.text}
                            </Button>
                        </div>
                    </Animate>
                )}
            </div>
        )}

        <div class="posts-grid">
            {filteredPosts.map((post, index) => (
                <div>
                    <BlogPost
                        title={post.data.title}
                        excerpt={post.data.excerpt}
                        featuredImage={post.data.featuredImage}
                        publishDate={post.data.publishDate}
                        categories={post.data.categories || []}
                        content={post.body}
                        slug={post.slug}
                        index={index}
                    />
                </div>
            ))}
        </div>

        {filteredPosts.length === 0 && (
            <p class="text-center text-gray-500 py-8">
                No posts found.
            </p>
        )}
    </div>
</section>

<style>
    .posts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .posts-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
