---
import { getBackgroundColor, getTextColor, getPaddingClass, getHeadlineColor, getEyebrowColor } from '@utils/styleUtils';
import type { ThemeColor, PaddingSize } from '@utils/styleUtils';
import Button from '@components/ui/Button.astro';
import Eyebrow from '@components/ui/Eyebrow.astro';
import Animate from '@components/ui/Animate.astro';

export interface Props {
    content: {
        eyebrow?: string;
        title: string;
        description?: string;
        button?: {
            text: string;
            link: string;
            variant?: 'primary' | 'secondary' | 'ghostLight' | 'ghostDark';
            target?: '_blank' | '_self';
        };
    };
    variant?: 'contained' | 'full-width';
    background?: ThemeColor;
    padding?: PaddingSize;
    paddingTop?: PaddingSize;
    paddingBottom?: PaddingSize;
}

const {
    content: { eyebrow, title, description, button },
    variant = 'contained',
    background = 'dark',
    padding,
    paddingTop,
    paddingBottom
} = Astro.props;

const bgColor = getBackgroundColor(background);
const textColor = getTextColor(background);
const headlineColor = getHeadlineColor(background);
const paddingClass = getPaddingClass({ padding, paddingTop, paddingBottom });

// Only this is specific to CtaBanner
const containerBgColors = {
    white: 'bg-background-alt',
    alt: 'bg-white',
    dark: 'bg-background-dark/50'
};

const containerBgColor = containerBgColors[background as keyof typeof containerBgColors];
---

<section class:list={[bgColor, paddingClass]}>
    <div class:list={[
        variant === 'contained' ? 'site-container px-4' : 'px-4',
        "text-center"
    ]}>
        <div class:list={[
            "mx-auto",
            variant === 'contained' ? 'max-w-3xl rounded-xl p-12' : 'max-w-4xl',
            variant === 'contained' && containerBgColor
        ]}>
            {eyebrow && (
                <Eyebrow
                    text={eyebrow}
                    background={background}
                />
            )}

            <Animate animation="fade-up">
                <h2 class:list={[headlineColor]}>
                    {title}
                </h2>
            </Animate>

            {description && (
                <Animate animation="fade-up" delay={100}>
                    <p class:list={["mt-4", textColor, "opacity-90"]}>
                        {description}
                    </p>
                </Animate>
            )}

            {button && (
                <Animate animation="fade-up" delay={200}>
                    <div class="mt-8">
                        <Button
                            href={button.link}
                            variant={button.variant || 'primary'}
                            target={button.target || '_self'}
                            size="md"
                        >
                            {button.text}
                        </Button>
                    </div>
                </Animate>
            )}
        </div>
    </div>
</section>
