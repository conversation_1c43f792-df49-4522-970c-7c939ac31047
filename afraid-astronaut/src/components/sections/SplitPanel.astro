---
import Button from '@components/ui/Button.astro';
import Eyebrow from '@components/ui/Eyebrow.astro';
import { getPaddingClass, getBackgroundColor, getTextColor } from '@utils/styleUtils';
import type { PaddingSize, ThemeColor } from '@utils/styleUtils';
import ResponsiveImage from '@components/ui/ResponsiveImage.astro';
import Animate from '@components/ui/Animate.astro';

interface Content {
    eyebrow?: string;
    headline: string;
    subheadline?: string;
    description?: string;
    image: {
        src: string | any;
        alt: string;
    };
    buttons?: {
        text: string;
        link: string;
        variant?: 'primary' | 'secondary' | 'ghostLight' | 'ghostDark';
        target?: string;
    }[];
}

export interface Props {
    content: Content;
    // Layout props
    imagePosition?: 'left' | 'right';
    background?: ThemeColor;
    paddingTop?: PaddingSize;
    paddingBottom?: PaddingSize;
    padding?: PaddingSize;
}

const {
    content,
    // Layout props
    imagePosition = 'right',
    background = 'base',
    paddingTop,
    paddingBottom,
    padding,
} = Astro.props;

const { eyebrow, headline, subheadline, description, image, buttons = [] } = content;

const paddingClass = getPaddingClass({ padding, paddingTop, paddingBottom });
const bgColor = getBackgroundColor(background);
const textColor = getTextColor(background);
---
<section class:list={["relative", bgColor, paddingClass]}>
    <div class="site-container px-4">
        <div class:list={[
            "grid gap-16 items-center",
            "md:grid-cols-2",
            imagePosition === 'left' ? 'md:grid-cols-[1fr_1fr]' : ''
        ]}>
            <!-- Image Side -->
            {imagePosition === 'left' && (
                <Animate animation="fade-up">
                    <div class="relative aspect-[4/3] overflow-hidden rounded-[var(--border-radius-base)] border border-black">
                        <ResponsiveImage
                            src={image.src}
                            alt={image.alt}
                            aspectRatio={4/3}
                            widths={[400, 800, 1200]}
                            sizes="(max-width: 768px) 100vw, 50vw"
                            class="w-full h-full object-cover"
                        />
                    </div>
                </Animate>
            )}

            <!-- Content Side -->
            <div class:list={[textColor]}>
                {eyebrow && (
                    <Eyebrow
                        text={eyebrow}
                        background={background}
                    />
                )}
                <Animate animation="fade-up">
                    <h2 class={textColor}>{headline}</h2>
                </Animate>
                {subheadline &&
                    <Animate animation="fade-up" delay={100}>
                        <h3 class:list={["mt-4 text-h5", textColor]}>{subheadline}</h3>
                    </Animate>
                }
                {description &&
                    <Animate animation="fade-up" delay={200}>
                        <p class:list={["mt-6 text-base opacity-90", textColor]}>{description}</p>
                    </Animate>
                }

                {buttons.length > 0 && (
                    <Animate animation="fade-up" delay={300}>
                        <div class="flex flex-wrap gap-4 mt-8">
                            {buttons.map((button) => (
                                <Button
                                    href={button.link}
                                    target={button.target || '_self'}
                                    variant={button.variant || 'primary'}
                                >
                                    {button.text}
                                </Button>
                            ))}
                        </div>
                    </Animate>
                )}
            </div>

            <!-- Image Side (right position) -->
            {imagePosition === 'right' && (
                <Animate animation="fade-up" delay={100}>
                    <div class="relative aspect-[4/3] overflow-hidden rounded-[var(--border-radius-base)] border border-black">
                        <ResponsiveImage
                            src={image.src}
                            alt={image.alt}
                            aspectRatio={4/3}
                            widths={[400, 800, 1200]}
                            sizes="(max-width: 768px) 100vw, 50vw"
                            class="w-full h-full object-cover"
                        />
                    </div>
                </Animate>
            )}
        </div>
    </div>
</section>
