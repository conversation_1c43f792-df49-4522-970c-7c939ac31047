---
import Animate from '@components/ui/Animate.astro';
import ResponsiveImage from '@components/ui/ResponsiveImage.astro';
import { getCollection } from 'astro:content';
import { getPaddingClass } from '@utils/styleUtils';
import type { PaddingSize } from '@utils/styleUtils';

interface Props {
    padding?: PaddingSize;
    paddingTop?: PaddingSize;
    paddingBottom?: PaddingSize;
}

const { padding, paddingTop, paddingBottom } = Astro.props;
const paddingClass = getPaddingClass({ padding, paddingTop, paddingBottom });

const teamMembers = await getCollection('team', ({ data }) => {
    return data.publish !== false;
});

// Sort team members by order
const sortedTeamMembers = teamMembers.sort((a, b) => a.data.order - b.data.order);
---

<section class:list={["relative", paddingClass]}>
    <div class="site-container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8 xl:gap-x-8 xl:gap-y-14">
            {sortedTeamMembers.map((member, index) => (
                <Animate animation="fade-up" delay={index * 100} duration={100}>
                    <div class="flex flex-col group">
                        <a href={`/team/${member.slug}`} class="flex flex-col group rounded-md">
                            <div class="relative mb-4 overflow-hidden rounded-md">
                                <span class="absolute inset-0 -mt-12 h-400 w-1/2 translate-x-[250%] rotate-12 bg-white opacity-20 group-hover:transition-all group-hover:duration-300 ease-out group-hover:translate-x-[-250%] z-10"></span>
                                {member.data.headshot ? (
                                    <ResponsiveImage
                                        src={member.data.headshot}
                                        alt={member.data.name}
                                        aspectRatio={1}
                                        class="w-full h-auto rounded-md group-hover:scale-[1.02] hover:brightness-110 transition-all duration-500 ease-in-out"
                                        widths={[896, 1792]}
                                        sizes="(max-width: 768px) 100vw, 896px"
                                    />
                                ) : (
                                    <div class="w-full aspect-[3/4] bg-gray-200 rounded-md flex items-center justify-center">
                                        <span class="text-gray-400">No Image</span>
                                    </div>
                                )}
                            </div>
                            <h3 class="text-h5 font-medium transition-colors duration-500 group-hover:text-primary">{member.data.name}</h3>
                            <p class="text-xsmall text-body-base">{member.data.jobTitle}</p>
                        </a>
                    </div>
                </Animate>
            ))}
        </div>
    </div>
</section>
