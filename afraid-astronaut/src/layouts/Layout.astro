---
import { ViewTransitions } from 'astro:transitions';
import '../styles/global.css';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import ThemeSwitcher from '../components/ThemeSwitcher.astro';
import { SEO, siteConfig } from '../data/config';
import { themeSetting } from '../data/config';
const { theme } = themeSetting;

const { SiteName, Separator, defaultDescription } = SEO;

interface Props {
	title?: string;
	description?: string;
	image?: string;
	footerCta?: {
		title: string;
		description: string;
		hideCta: boolean;
		button: {
			text: string;
			link: string;
			target?: string;
		};
	};
	canonicalUrl?: string;
	useAnimations?: boolean;
}

const {
	title,
	description,
	image,
	footerCta,
	canonicalUrl,
	useAnimations = true
} = Astro.props;

const pageTitle = title ? `${title} ${Separator} ${SiteName}` : SiteName;
const pageDescription = description || defaultDescription || '';
const pageImage = image || '';
const canonicalPath = canonicalUrl || Astro.url.pathname;
const fullCanonicalUrl = new URL(canonicalPath, siteConfig.siteUrl).toString();

---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/x-icon" href="/favicon.ico" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
		<link rel="sitemap" href="/sitemap-index.xml" />
		<link rel="canonical" href={fullCanonicalUrl} />
		<meta name="generator" content={Astro.generator} />
		<title>{pageTitle}</title>
		<meta name="description" content={pageDescription} />
		<meta name="robots" content="index, follow" />
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Bruno+Ace+SC&family=Noto+Sans+KR:wght@100..900&display=swap" rel="stylesheet">

		<!-- Add Astro View Transitions -->
		<ViewTransitions />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={fullCanonicalUrl} />
		<meta property="og:title" content={pageTitle} />
		<meta property="og:description" content={pageDescription} />
		{pageImage && <meta property="og:image" content={new URL(pageImage, siteConfig.siteUrl).toString()} />}

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={fullCanonicalUrl} />
		<meta property="twitter:title" content={pageTitle} />
		<meta property="twitter:description" content={pageDescription} />
		{pageImage && <meta property="twitter:image" content={new URL(pageImage, siteConfig.siteUrl).toString()} />}
	</head>
	<body data-theme={theme} transition:animate="fade">
		<Header transition:persist />
		<main>
			<slot />
		</main>
		<Footer footerCta={footerCta} transition:persist />
		<ThemeSwitcher transition:persist />
	</body>
</html>
