---
import { getCollection } from 'astro:content';
import Layout from '@layouts/Layout.astro';
import InnerHero from '@components/sections/InnerHero.astro';
import MobileProfile from '@components/team/MobileProfile.astro';
import DesktopProfile from '@components/team/DesktopProfile.astro';

export async function getStaticPaths() {
  const teamEntries = await getCollection('team');
  return teamEntries.map(entry => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await entry.render();
const heroContent = {
  title: entry.data.name,
  description: entry.data.jobTitle,
}
---

<Layout title={entry.data.name} description={`Team member: ${entry.data.name}`}>
  <InnerHero content={heroContent} />
  <div class="site-container mx-auto px-4">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12 pt-8 lg:pt-0">
      <!-- Profile Sidebar -->
      <div class="lg:col-span-1 relative">
        <div class="absolute inset-0 bg-white border-l border-r border-black lg:block hidden"></div>
        <div class="lg:sticky lg:top-24">
          <div class="relative lg:p-6">
            <div class="flex flex-col lg:flex-col gap-6 lg:gap-4">
              <MobileProfile entry={entry} />
              <DesktopProfile entry={entry} />
            </div>
          </div>
        </div>
      </div>
      
      <!-- Main Content -->
      <div class="lg:col-span-2 py-base">
        <article>
          <Content />
        </article>
      </div>
    </div>
  </div>
</Layout> 