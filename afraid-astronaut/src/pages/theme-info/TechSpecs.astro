---
const components = [
    {
        name: "CTA Banner",
        description: "A versatile call-to-action banner with customizable background, content, and button options. Supports both contained and full-width variants.",
    },
    {
        name: "Contact Info",
        description: "Displays contact information alongside a contact form. Includes email, phone, and location with icons.",
    },
    {
        name: "FAQs",
        description: "An animated accordion-style FAQ section with expandable questions and answers. Supports custom styling and animations.",
    },
    {
        name: "Features",
        description: "A grid layout for showcasing features or services with icons, titles, and descriptions. Supports various column layouts.",
    },
    {
        name: "Hero",
        description: "A full-width hero section with title, description, and call-to-action button. Includes background image support.",
    },
    {
        name: "Inner Hero",
        description: "A compact hero section for inner pages with title and optional subtitle. Perfect for page headers.",
    },
    {
        name: "Logos",
        description: "A logo grid for displaying partner or client logos with hover effects and grayscale transitions.",
    },
    {
        name: "Recent Posts",
        description: "A list of recent posts with titles, excerpts, and featured images. Supports custom styling and animations.",
    },
    {
        name: "Split Panel",
        description: "A two-column layout combining image and content. Supports left/right image positioning and multiple buttons.",
    },
    {
        name: "Stats",
        description: "Animated statistics display with counter effects. Supports both grid and side-by-side layouts with optional content.",
    },
    {
        name: "Team Grid",
        description: "A responsive grid for team members with hover effects and linking to individual profiles.",
    }
]

const collections = [
    {
        name: "blog",
        description: "A blog page with a list of posts and a single post page.",
    },
    {
        name: "team",
        description: "A team page with a list of team members and a single team member page.",
    },
    {
        name: "legal",
        description: "A collection of legal documents like Privacy Policy and Terms of Service using Markdown content.",
    }
]

const techStack = [
    {
        name: "Astro",
        version: "^5.2.5",
        description: "Modern static site builder with island architecture",
    },
    {
        name: "Tailwind CSS",
        version: "^4.0.5",
        description: "Utility-first CSS framework",
    },
    {
        name: "Lucide Icons",
        version: "^0.473.0",
        description: "Beautiful & consistent icon pack",
    },
    {
        name: "Astro Sitemap",
        version: "^3.2.1",
        description: "Automatic XML sitemap generation for SEO optimization",
    }
];

const specs = [
    {
        name: "Build System",
        description: "Static site generation with dynamic islands",
    },
    {
        name: "Performance",
        description: "Zero JS by default, selective hydration for interactive components",
    },
    {
        name: "Styling",
        description: "Tailwind CSS with custom theme configuration",
    },
    {
        name: "TypeScript",
        description: "Full TypeScript support with type safety",
    },
    {
        name: "Asset Handling",
        description: "Optimized image processing and asset bundling",
    },
    {
        name: "SEO",
        description: "Configured XML sitemap with weekly changefreq and 404 page exclusion",
    }
];

---

<section class="py-small">
    <div class="site-container px-8">
        <div class="space-y-12">

            <div>
                <h2 class="text-large mb-6">Available Components</h2>
                <ul class="space-y-3">
                    {components.map((component) => (
                        <li>
                            <code class="text-sm">{component.name}</code>
                            <span class="mx-2 text-accent">—</span>
                            <span class="text-sm text-body-base">{component.description}</span>
                        </li>
                    ))}
                </ul>
            </div>

            <div>
                <h2 class="text-large mb-6">Configured Collections</h2>
                <ul class="space-y-3">
                    {collections.map((collection) => (
                        <li>
                            <code class="text-sm">{collection.name}</code>
                            <span class="mx-2 text-accent">—</span>
                            <span class="text-sm text-body-base">{collection.description}</span>
                        </li>
                    ))}
                </ul>
            </div>

            <div>
                <h2 class="text-large mb-6">Libraries & Dependencies</h2>
                <ul class="space-y-3">
                    {techStack.map((tech) => (
                        <li>
                            <code class="text-sm">{tech.name}</code>
                            <span class="mx-2 text-accent">—</span>
                            <span class="text-sm text-body-base">{tech.description}</span>
                            <span class="ml-2 text-xs text-body-base/80">{tech.version}</span>
                        </li>
                    ))}
                </ul>
            </div>

            <div>
                <h2 class="text-large mb-6">Technical Specifications</h2>
                <ul class="space-y-3">
                    {specs.map((spec) => (
                        <li>
                            <code class="text-sm">{spec.name}</code>
                            <span class="mx-2 text-accent">—</span>
                            <span class="text-sm text-body-base">{spec.description}</span>
                        </li>
                    ))}
                </ul>
            </div>
        </div>
    </div>
</section>