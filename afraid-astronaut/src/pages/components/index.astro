---
import Layout from '@layouts/Layout.astro';
import Hero from '@components/sections/Hero.astro';
import SplitPanel from '@components/sections/SplitPanel.astro';
import Features from '@components/sections/Features.astro';
import Stats from '@components/sections/Stats.astro';
import { featureLists } from '@data/features';
import { statsLists } from '@data/stats';
import Logos from '@components/sections/Logos.astro';
import { logoLists } from '@data/logos';
import CtaBanner from '@components/sections/CtaBanner.astro';
import Faqs from '@components/sections/Faqs.astro';
import { faqLists } from '@data/faqs';
import RecentPosts from '@components/sections/RecentPosts.astro';
import InnerHero from '@components/sections/InnerHero.astro';
import TeamGrid from '@components/sections/TeamGrid.astro';
import defaultHero from '@assets/images/home/<USER>';
import defaultHero2 from '@assets/images/home/<USER>';
import developerImg from '@assets/images/home/<USER>';

const seoTitle = "Component Library - Titan Core";
const seoDescription = "Explore the complete component library of Titan Core, a modern, high-performance Astro theme for developer portfolios and documentation sites.";

// Define the component sections for the navigation
const componentSections = [
    { id: 'hero', name: 'Hero', icon: 'layout' },
    { id: 'innerHero', name: 'Inner Hero', icon: 'layout-template' },
    { id: 'features', name: 'Features', icon: 'package' },
    { id: 'recentPosts', name: 'Recent Posts', icon: 'file-text' },
    { id: 'splitPanel', name: 'Split Panel', icon: 'columns' },
    { id: 'ctaBanner', name: 'CTA Banner', icon: 'megaphone' },
    { id: 'logos', name: 'Logos', icon: 'briefcase' },
    { id: 'stats', name: 'Stats', icon: 'bar-chart' },
    { id: 'faqs', name: 'FAQs', icon: 'help-circle' },
    { id: 'teamGrid', name: 'Team Grid', icon: 'users' }
];

// Hero Component Content
const heroContent = {
    title: 'Create Stunning Hero Sections',
    description: 'The Hero component is perfect for creating impactful first impressions. It supports background images, overlay opacity control, and multiple call-to-action buttons to drive user engagement.',
    buttons: [
        {
            text: 'Primary Action',
            link: '/contact',
            target: '_self'
        },
        {
            text: 'Secondary Action',
            link: 'https://github.com/titan-studio-themes/titan-core',
            target: '_blank'
        }
    ],
    backgroundImage: defaultHero2,
    overlayOpacity: 0.2
}

// Inner Hero Component Content
const innerHeroContent = {
    title: 'Inner Hero for Secondary Pages',
    description: 'A more compact hero component designed specifically for inner pages, providing consistent branding while taking up less vertical space.',
    backgroundImage: defaultHero,
    overlayOpacity: 0.5
}

// Features Component Content
const featuresSection = {
    eyebrow: "COMPONENT FEATURES",
    title: "Showcase Product Features & Benefits",
    description: "The Features component is ideal for highlighting key features, benefits, or services in a visually appealing grid layout with icons and descriptions.",
    features: featureLists.main.features,
    button: {
        text: "View All Features",
        link: "/features",
        variant: "primary" as const
    }
}

// Footer CTA Content
const FooterCta = {
    eyebrow: "GET STARTED TODAY",
    title: 'Start Building With Titan Core',
    description: 'Titan Core is open-source and constantly improving. Join our community of developers to contribute, get support, or showcase what you\'ve built.',
    hideCta: false,
    button: {
        text: 'Download Titan Core',
        link: 'https://github.com/titan-studio-themes/titan-core',
        target: '_blank'
    }
}

// Split Panel Component Content
const styleGuideSection = {
    eyebrow: "FLEXIBLE LAYOUTS",
    headline: "Text & Image Combinations",
    description: "The SplitPanel component allows you to create beautiful side-by-side layouts with text on one side and images on the other. Perfect for product showcases, about sections, or feature highlights.",
    image: {
        src: developerImg,
        alt: "Developer working on code"
    },
    buttons: [
        {
            text: "Learn More",
            link: "/split-panel",
            variant: "primary" as const
        }
    ]
}

// Stats Component Content
const sideBySideStats = {
    eyebrow: "IMPRESSIVE NUMBERS",
    title: "Showcase Key Metrics & Achievements",
    description: "The Stats component helps you highlight important metrics, achievements, or growth numbers in a visually compelling way that draws attention to your success.",
    variant: 'side-by-side' as const,
    background: 'dark' as const,
    stats: statsLists.withContent.stats,
    content: {
        title: 'Performance Metrics',
        description: 'Titan Core is designed for speed and efficiency. Our lightweight framework ensures fast load times and smooth user experiences.',
        button: {
            text: 'Learn More',
            link: '/performance',
            variant: 'primary' as const
        }
    }
}

// CTA Banner Component Content
const mainCta = {
    eyebrow: "CALL TO ACTION",
    title: "Drive User Engagement",
    description: "The CTA Banner component is designed to convert visitors into customers with compelling headlines, persuasive copy, and prominent call-to-action buttons.",
    button: {
        text: "Take Action Now",
        link: "/contact",
        variant: "primary" as const,
        target: "_self" as const
    }
};

// Logos Component Content
const logosSection = {
    eyebrow: "SOCIAL PROOF",
    headline: "Build Trust With Brand Logos",
    subheadline: "The Logos component displays partner or client logos to build credibility and trust with your visitors.",
    logos: logoLists.main.logos
}

// FAQs Component Content
const faqsSection = {
    eyebrow: "COMMON QUESTIONS",
    title: 'Answer Customer Questions',
    description: 'The FAQs component helps address common questions and concerns, reducing support inquiries while building trust and confidence in your product or service.',
    faqs: faqLists.main.faqs
}

// Recent Posts Component Content
const recentPostsSection = {
    eyebrow: "CONTENT MARKETING",
    title: "Showcase Your Latest Content",
    description: "The RecentPosts component automatically displays your latest blog posts or articles, helping drive traffic to your content and keeping your site fresh and engaging.",
    button: {
        text: "View All Posts",
        link: "/blog",
        variant: "primary" as const
    }
}

---

<Layout footerCta={FooterCta} title={seoTitle} description={seoDescription}>
    <!-- Floating Side Navigation -->
    <div class="fixed left-4 top-1/2 transform -translate-y-1/2 z-50 hidden lg:block">
        <div class="bg-white rounded-lg shadow-xl p-3 w-52">
            <div class="flex items-center mb-3 pb-2 border-b">
                <h3 class="font-bold text-sm">Components</h3>
            </div>
            <nav>
                <ul class="space-y-1">
                    {componentSections.map(section => (
                        <li>
                            <a 
                                href={`#${section.id}`} 
                                class="component-nav-link flex items-center p-1.5 rounded-md hover:bg-gray-100 transition-colors text-xs"
                                data-section={section.id}
                            >
                                <span class="icon mr-1.5" data-lucide={section.icon}></span>
                                <span>{section.name}</span>
                            </a>
                        </li>
                    ))}
                </ul>
            </nav>
        </div>
    </div>

    <!-- Component Sections -->
    <section id="hero" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Hero Component
            </div>
            <Hero content={heroContent} />
        </div>
    </section>
    
    <section id="innerHero" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Inner Hero Component
            </div>
            <InnerHero content={innerHeroContent} />
        </div>
    </section>
    
    <section id="features" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Features Component
            </div>
            <Features content={featuresSection} padding="base" background="light" />
        </div>
    </section>
    
    <section id="recentPosts" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Recent Posts Component
            </div>
            <RecentPosts content={recentPostsSection} background="base" padding="base" postsToShow={3} category="Theme Usage"/>
        </div>
    </section>
    
    <section id="splitPanel" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Split Panel Component
            </div>
            <SplitPanel content={styleGuideSection} imagePosition="right" background="light" padding="base" />
        </div>
    </section>
    
    <section id="ctaBanner" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                CTA Banner Component
            </div>
            <CtaBanner content={mainCta} variant="contained" background="light" padding="base" />
        </div>
    </section>
    
    <section id="logos" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Logos Component
            </div>
            <Logos content={logosSection} background="base" padding='base'/>
        </div>
    </section>
    
    <section id="stats" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Stats Component
            </div>
            <Stats content={sideBySideStats} background='dark' padding="base"/>
        </div>
    </section>
    
    <section id="faqs" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                FAQs Component
            </div>
            <Faqs content={faqsSection} background="base" padding="base" />
        </div>
    </section>
    
    <section id="teamGrid" class="component-section relative mb-16">
        <div class="component-wrapper relative transition-all duration-500">
            <div class="component-label hidden absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white py-1 px-3 rounded-md text-xs font-medium z-10 shadow-md">
                Team Grid Component
            </div>
            <TeamGrid padding="base" />
        </div>
    </section>
</Layout>

<script>
    // Wait for the document to be fully loaded
    document.addEventListener('DOMContentLoaded', () => {
        // Get all section elements
        const sections = document.querySelectorAll('.component-section');
        const navLinks = document.querySelectorAll('.component-nav-link');
        const componentWrappers = document.querySelectorAll('.component-wrapper');
        const componentLabels = document.querySelectorAll('.component-label');
        
        // Initialize Lucide icons
        // @ts-ignore - Lucide is loaded globally but TypeScript doesn't know about it
        if (typeof window.lucide !== 'undefined') {
            // @ts-ignore
            window.lucide.createIcons();
        }
        
        // Function to update active navigation link and component
        const updateActiveLink = () => {
            // Get current scroll position
            const scrollPosition = window.scrollY;
            
            // Find the current section
            sections.forEach((section, index) => {
                const sectionTop = section.getBoundingClientRect().top + window.scrollY;
                const sectionHeight = section.getBoundingClientRect().height;
                const sectionId = section.getAttribute('id');
                
                if (sectionId && scrollPosition >= sectionTop - 200 && scrollPosition < sectionTop + sectionHeight - 200) {
                    // Remove active class from all links
                    navLinks.forEach(link => {
                        link.classList.remove('bg-primary', 'text-white');
                    });
                    
                    // Reset all component wrappers
                    componentWrappers.forEach((wrapper, i) => {
                        wrapper.classList.remove('border-4', 'border-primary', 'shadow-lg', 'bg-white');
                        componentLabels[i].classList.add('hidden');
                    });
                    
                    // Add active class to current section link
                    const activeLink = document.querySelector(`.component-nav-link[data-section="${sectionId}"]`);
                    if (activeLink) {
                        activeLink.classList.add('bg-primary', 'text-white');
                    }
                    
                    // Highlight active component
                    const activeWrapper = componentWrappers[index];
                    const activeLabel = componentLabels[index];
                    if (activeWrapper) {
                        activeWrapper.classList.add('border-4', 'border-primary', 'shadow-lg', 'bg-white');
                        activeLabel.classList.remove('hidden');
                    }
                }
            });
        };
        
        // Add scroll event listener
        window.addEventListener('scroll', updateActiveLink);
        
        // Initial call to set active link on page load
        updateActiveLink();
        
        // Add smooth scrolling for navigation links
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                if (!href) return;
                
                const targetId = href.substring(1);
                const targetSection = document.getElementById(targetId);
                
                if (targetSection) {
                    const targetTop = targetSection.getBoundingClientRect().top + window.scrollY;
                    window.scrollTo({
                        top: targetTop - 100,
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>

<style>
    .component-section {
        scroll-margin-top: 100px;
    }
    
    .component-wrapper {
        position: relative;
        z-index: 1;
    }
</style>