---
import Layout from '@layouts/Layout.astro';
import Button from '@components/ui/Button.astro';
import Animate from '@components/ui/Animate.astro';

const seoTitle = "Page Not Found";
const seoDescription = "The page you're looking for doesn't exist or has been moved.";
---

<Layout title={seoTitle} description={seoDescription}>
    <div class="w-full min-h-[80vh] flex items-center">
        <div class="site-container mx-auto px-4 py-base text-center">
            <div class="max-w-2xl mx-auto">
                <Animate animation="fade-up">
                    <h1 class="mb-4 text-9xl font-bold text-primary">404</h1>
                </Animate>
                <Animate animation="fade-up" delay={100}>
                    <h2 class="mb-8">Page Not Found</h2>
                </Animate>
                <Animate animation="fade-up" delay={200}>
                    <p class="text-body-base mb-12">
                        The page you're looking for doesn't exist or has been moved.
                        Please check the URL or navigate back to our homepage.
                    </p>
                </Animate>
                <Animate animation="fade-up" delay={300}>
                    <Button
                        href="/"
                        variant="primary"
                        size="lg"
                    >
                        Back to Homepage
                    </Button>
                </Animate>
            </div>
        </div>
    </div>
</Layout>
