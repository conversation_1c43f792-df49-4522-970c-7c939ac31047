---
import { getCollection } from 'astro:content';
import PaginatedBlogLayout from '../../components/blog/PaginatedBlogLayout.astro';
import { categories } from '../../data/categories';
import { blogSetting } from '../../data/config';

export async function getStaticPaths() {
  // Get all blog posts
const allPosts = await getCollection('blog', ({ data }) => {
    return import.meta.env.DEV || data.publish !== false;
});

// Get categories that have at least one posts
const activeCategories = categories.filter(category => 
    allPosts.some(post => post.data.categories?.includes(category.name))
);

// Generate paths only for categories with posts
return activeCategories.map((category) => ({
    params: { slug: category.slug },
    props: { category },
}));
}

const { category } = Astro.props;
const currentPage = parseInt(Astro.url.searchParams.get('page') || '1');

// Get posts for this category
const posts = await getCollection('blog', ({ data }) => {
const isPublished = import.meta.env.DEV || data.publish !== false;
const hasCategory = data.categories?.includes(category.name);
return isPublished && hasCategory;
});

// Sort posts by date
const sortedPosts = posts.sort((a, b) => 
b.data.publishDate.valueOf() - a.data.publishDate.valueOf()
);

// Calculate pagination
const totalPosts = sortedPosts.length;
const totalPages = Math.ceil(totalPosts / blogSetting.postsPerPage);
const start = (currentPage - 1) * blogSetting.postsPerPage;
const end = start + blogSetting.postsPerPage;
const paginatedPosts = sortedPosts.slice(start, end);
---

<PaginatedBlogLayout 
    posts={paginatedPosts}
    currentPage={currentPage}
    totalPages={totalPages}
    baseUrl={`/category/${category.slug}`}
    title={category.name}
    subtitle={category.description}
    currentCategory={category.name} /> 