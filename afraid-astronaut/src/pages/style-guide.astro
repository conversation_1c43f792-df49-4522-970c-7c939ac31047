---
import Layout from '@layouts/Layout.astro';
import Button from '@components/ui/Button.astro';
import InnerHero from '@components/sections/InnerHero.astro';
import { getBackgroundColor, getTextColor, getHeadlineColor, getEyebrowColor } from '@utils/styleUtils';
import Input from '@components/ui/form/Input.astro';
import Textarea from '@components/ui/form/Textarea.astro';
import Select from '@components/ui/form/Select.astro';
import Checkbox from '@components/ui/form/Checkbox.astro';
import Radio from '@components/ui/form/Radio.astro';

const title = 'Style Guide';
const description = 'A style guide for Astro Basics';

const heroContent = {
    title: title,
    description: description,
}

const backgrounds = ['base', 'light', 'dark'] as const;

---

<Layout title={title} description={description}>
    <InnerHero content={heroContent} />

    {/* Colors Section - Show Once */}
    <div class="max-w-4xl mx-auto px-4 py-12">
        <section id="colors" class="mb-12">
            <h2 class="text-2xl font-bold text-headline mb-6">Colors</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                    <div class="h-24 bg-primary rounded-lg mb-2"></div>
                    <p class="text-sm font-medium text-body-base">Primary</p>
                </div>
                <div>
                    <div class="h-24 bg-secondary rounded-lg mb-2"></div>
                    <p class="text-sm font-medium text-body-base">Secondary</p>
                </div>
                <div>
                    <div class="h-24 bg-accent rounded-lg mb-2"></div>
                    <p class="text-sm font-medium text-body-base">Accent</p>
                </div>
                <div>
                    <div class="h-24 rounded-lg mb-2 bg-background-dark"></div>
                    <p class="text-sm font-medium text-body-base">Dark</p>
                </div>
            </div>
        </section>
    </div>

    {/* Tabs Section */}
    <div class="max-w-4xl mx-auto px-4 mb-8">
        <h2 class="text-2xl font-bold mb-6 text-headline">Background Variants</h2>
        <div class="flex space-x-4 rounded-xl p-4 bg-background-white border border-background-dark">
            {backgrounds.map(bg => (
                <button 
                    type="button"
                    class={`
                        w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-center transition-colors cursor-pointer
                        border border-primary
                        ${bg === 'base' ? 'bg-background text-headline' : ''}
                        ${bg === 'light' ? 'bg-background-light text-headline-light' : ''}
                        ${bg === 'dark' ? 'bg-background-dark text-headline-dark' : ''}
                        hover:opacity-90
                    `}
                    data-tab={bg}
                >
                    {bg === 'base' ? 'Default' : bg.charAt(0).toUpperCase() + bg.slice(1)}
                </button>
            ))}
        </div>
    </div>

    {/* Content Sections */}
    {backgrounds.map(bg => (
        <div 
            data-content={bg}
            class={`${getBackgroundColor(bg)} ${getTextColor(bg)} py-12 ${bg === 'base' ? '' : 'hidden'}`}
        >
            <div class="max-w-4xl mx-auto px-4">
                <h2 class={`text-3xl font-bold mb-8 ${getHeadlineColor(bg)}`}>
                    Style Guide - {bg.charAt(0).toUpperCase() + bg.slice(1)} Background
                </h2>

                {/* Typography Section */}
                <section id={`typography-${bg}`} class="mb-12">
                    <h2 class={`text-2xl font-bold mb-6 ${getHeadlineColor(bg)}`}>Typography</h2>
                    <div class="space-y-4">
                        <h1 class={getHeadlineColor(bg)}>Heading 1</h1>
                        <h2 class={getHeadlineColor(bg)}>Heading 2</h2>
                        <h3 class={getHeadlineColor(bg)}>Heading 3</h3>
                        <h4 class={getHeadlineColor(bg)}>Heading 4</h4>
                        <h5 class={getHeadlineColor(bg)}>Heading 5</h5>
                        <h6 class={getHeadlineColor(bg)}>Heading 6</h6>
                        
                        <div class="my-8">
                            <p class={`text-sm uppercase tracking-wider font-medium ${getEyebrowColor(bg)}`}>Eyebrow Text - Small uppercase text used above headlines</p>
                        </div>
                        
                        <div class="my-8">
                            <p class="mb-4">Body text - Regular paragraph text looks like this. Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
                            <p class="text-sm mb-4">Small text - Smaller paragraph text looks like this.</p>
                            <p class="text-xs">Extra small text - The smallest text size we use.</p>
                        </div>
                    </div>
                </section>

                {/* Links Section */}
                <section id={`links-${bg}`} class="mb-12">
                    <h2 class={`text-2xl font-bold mb-6 ${getHeadlineColor(bg)}`}>Links</h2>
                    <div class="space-y-4">
                        <p><a href="#" class="text-primary hover:underline">Regular Link</a></p>
                        <p><a href="#" class="text-primary hover:underline font-bold">Bold Link</a></p>
                        <p><a href="#" class="text-primary underline hover:no-underline">Underlined Link</a></p>
                    </div>
                </section>

                {/* Buttons Section */}
                <section id={`buttons-${bg}`} class="mb-12">
                    <h2 class={`text-2xl font-bold mb-6 ${getHeadlineColor(bg)}`}>Buttons</h2>
                    <div class="space-y-4">
                        <div class="flex flex-col gap-4 items-start">
                            <Button variant="primary" size="md">Primary Button</Button>
                            <Button variant="secondary" size="md">Secondary Button</Button>
                            <Button variant="accent" size="md">Accent Button</Button>
                            <Button disabled>Disabled Button</Button>
                        </div>
                    </div>
                </section>

                {/* Form Elements */}
                <section id={`forms-${bg}`} class="mb-12">
                    <h2 class={`text-2xl font-bold mb-6 ${getHeadlineColor(bg)}`}>Form Elements</h2>
                    <div class="space-y-6 max-w-md">
                        <Input
                            label="Text Input"
                            id={`text-input-${bg}`}
                            name="text-input"
                            placeholder="Enter some text"
                            background={bg}
                        />

                        <Input
                            label="Email Input"
                            type="email"
                            id={`email-input-${bg}`}
                            name="email-input"
                            placeholder="Enter your email"
                            background={bg}
                        />

                        <Textarea
                            label="Textarea"
                            id={`textarea-${bg}`}
                            name="textarea"
                            placeholder="Enter your message"
                            background={bg}
                        />

                        <Select
                            label="Select Menu"
                            id={`select-${bg}`}
                            name="select"
                            options={[
                                { label: 'Option 1', value: 'option1' },
                                { label: 'Option 2', value: 'option2' },
                                { label: 'Option 3', value: 'option3' },
                            ]}
                            background={bg}
                        />

                        <Checkbox
                            label="Single Checkbox"
                            id={`checkbox-single-${bg}`}
                            name="checkbox-single"
                            background={bg}
                        />

                        <Checkbox
                            label="Multiple Checkboxes"
                            id={`checkbox-multiple-${bg}`}
                            name="checkbox-multiple"
                            options={[
                                { label: 'Option 1', value: 'option1' },
                                { label: 'Option 2', value: 'option2' },
                                { label: 'Option 3', value: 'option3' },
                            ]}
                            background={bg}
                        />

                        <Radio
                            label="Radio Buttons"
                            id={`radio-${bg}`}
                            name={`radio-${bg}`}
                            options={[
                                { label: 'Option 1', value: 'option1' },
                                { label: 'Option 2', value: 'option2' },
                                { label: 'Option 3', value: 'option3' },
                            ]}
                            background={bg}
                        />

                        <div class="space-y-2">
                            <Input
                                label="Required Input"
                                id={`required-input-${bg}`}
                                name="required-input"
                                required={true}
                                placeholder="This field is required"
                                background={bg}
                            />

                            <Input
                                label="Disabled Input"
                                id={`disabled-input-${bg}`}
                                name="disabled-input"
                                disabled={true}
                                placeholder="This field is disabled"
                                background={bg}
                            />
                        </div>
                    </div>
                </section>
            </div>
        </div>
    ))}

    <script>
        // Handle tab clicks
        document.querySelectorAll('[data-tab]').forEach(tab => {
            tab.addEventListener('click', () => {
                const targetBg = tab.getAttribute('data-tab');
                
                // Update active tab state
                document.querySelectorAll('[data-tab]').forEach(t => {
                    t.classList.remove('ring-2', 'ring-offset-2');
                });
                tab.classList.add('ring-2', 'ring-offset-2');
                
                // Show selected content, hide others
                document.querySelectorAll('[data-content]').forEach(content => {
                    content.classList.add('hidden');
                });
                document.querySelector(`[data-content="${targetBg}"]`)?.classList.remove('hidden');
            });
        });

        // Set initial active state
        document.querySelector('[data-tab="white"]')?.classList.add('ring-2', 'ring-offset-2');
    </script>
</Layout>