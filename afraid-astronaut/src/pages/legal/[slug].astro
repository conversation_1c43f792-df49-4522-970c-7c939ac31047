---
import { getCollection } from 'astro:content';
import Layout from '../../layouts/Layout.astro';
import InnerHero from '../../components/sections/InnerHero.astro';

export async function getStaticPaths() {
  const legalEntries = await getCollection('legal');
  return legalEntries.map(entry => ({
    params: { slug: entry.slug },
    props: { entry },
  }));
}

const { entry } = Astro.props;
const { Content } = await entry.render();

// Use SEO data from frontmatter if available, fallback to regular entry data
const seoTitle = entry.data.seo?.title || entry.data.title;
const seoDescription = entry.data.seo?.description || '';

// Format the last updated date
const formattedDate = new Intl.DateTimeFormat('en-US', {
  year: 'numeric',
  month: 'long',
  day: 'numeric'
}).format(entry.data.lastUpdated);

// Hero content
const heroContent = {
  title: entry.data.title,
  description: '',
  overlayOpacity: 0.8
};
---

<Layout 
  title={seoTitle}
  description={seoDescription}
>
  <InnerHero content={heroContent} />
  
  <article class="site-container--small mx-auto px-8 prose pb-base">
    <div class="mt-12 mb-8">
      <p class="text-sm text-body-base/80">Last updated: {formattedDate}</p>
    </div>
    
    <Content />
  </article>
</Layout> 