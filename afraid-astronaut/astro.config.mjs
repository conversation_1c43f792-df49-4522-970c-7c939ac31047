import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import sitemap from '@astrojs/sitemap';

// Get the site URL from environment variable or use a default for local development
const site = process.env.PUBLIC_SITE_URL || 'http://localhost:4321';

export default defineConfig({
  site,
  vite: {
    plugins: [tailwindcss()],
    optimizeDeps: {
      include: []
    }
  },
  markdown: {
    shikiConfig: {
      theme: 'github-dark',
      wrap: true,
      langs: [],
      transformers: [],
      showLineNumbers: false,
      lineNumbersPrefix: ''
    }
  },
  integrations: [
    sitemap({
      changefreq: 'weekly',
      priority: 0.7,
      lastmod: new Date(),
      filter: (page) => !page.includes('/404'), // Only exclude 404 page
      entryLimit: 10000, // Increase entry limit if you have many pages
    }),
  ],
  image: {
    // Optimize service
    service: { entrypoint: 'astro/assets/services/sharp' },
    // Default image quality
    defaultQuality: 80,
    // Allow all remote patterns (https and http)
    remotePatterns: [
      { protocol: "https" },
      { protocol: "http" }
    ],
    // Add format preferences
    format: ['avif', 'webp']
  }
});
